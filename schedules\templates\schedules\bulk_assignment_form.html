{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Phân công lịch trực hàng loạt{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Phân công lịch trực hàng loạt</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:assignment_list' %}">Phân công lịch trực</a></li>
          <li class="breadcrumb-item active">Phân công hàng loạt</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">Phân công lịch trực hàng loạt</h3>
          </div>
          
          <form method="post" action="{% url 'schedules:bulk_assignment_create' %}">
            {% csrf_token %}
            <div class="card-body">
              {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
              
              <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Công cụ này cho phép bạn phân công nhiều nhân viên vào cùng một ca trực trong một khoảng thời gian.
              </div>
              
              <div class="form-group">
                <label for="{{ form.schedule.id_for_label }}">Lịch trực <span class="text-danger">*</span></label>
                {{ form.schedule }}
                {% if form.schedule.errors %}
                <div class="text-danger">
                  {% for error in form.schedule.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.shift.id_for_label }}">Ca trực <span class="text-danger">*</span></label>
                {{ form.shift }}
                {% if form.shift.errors %}
                <div class="text-danger">
                  {% for error in form.shift.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.start_date.id_for_label }}">Ngày bắt đầu <span class="text-danger">*</span></label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="text-danger">
                      {% for error in form.start_date.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.end_date.id_for_label }}">Ngày kết thúc <span class="text-danger">*</span></label>
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                    <div class="text-danger">
                      {% for error in form.end_date.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label for="{{ form.users.id_for_label }}">Nhân viên <span class="text-danger">*</span></label>
                {{ form.users }}
                {% if form.users.errors %}
                <div class="text-danger">
                  {% for error in form.users.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">Có thể chọn nhiều nhân viên.</small>
              </div>
              
              <div class="form-group">
                <div class="custom-control custom-checkbox">
                  {{ form.is_confirmed }}
                  <label class="custom-control-label" for="{{ form.is_confirmed.id_for_label }}">Đã xác nhận</label>
                </div>
                {% if form.is_confirmed.errors %}
                <div class="text-danger">
                  {% for error in form.is_confirmed.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Lưu ý: Hệ thống sẽ bỏ qua các phân công trùng lặp (cùng nhân viên, cùng ca trực, cùng ngày).
              </div>
            </div>
            
            <div class="card-footer">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Lưu
              </button>
              <a href="{% url 'schedules:assignment_list' %}" class="btn btn-default">
                <i class="fas fa-times"></i> Hủy
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });
    
    // Xử lý khi thay đổi lịch trực
    $('#{{ form.schedule.id_for_label }}').on('change', function() {
      const scheduleId = $(this).val();
      
      if (scheduleId) {
        // Lấy danh sách nhân viên theo khoa/phòng ban của lịch trực
        $.ajax({
          url: "{% url 'schedules:get_users_by_department' %}",
          type: 'GET',
          data: {
            'department_id': $('#{{ form.schedule.id_for_label }} option:selected').data('department-id')
          },
          dataType: 'json',
          success: function(data) {
            // Cập nhật danh sách nhân viên
            const userSelect = $('#{{ form.users.id_for_label }}');
            userSelect.empty();
            
            $.each(data.users, function(index, user) {
              userSelect.append(new Option(user.name, user.id));
            });
            
            // Trigger change để cập nhật Select2
            userSelect.trigger('change');
          },
          error: function() {
            console.error('Không thể lấy danh sách nhân viên.');
          }
        });
        
        // Lấy thông tin lịch trực
        $.ajax({
          url: "/schedules/schedule/" + scheduleId + "/",
          type: 'GET',
          dataType: 'json',
          success: function(data) {
            // Cập nhật ngày bắt đầu và kết thúc
            $('#{{ form.start_date.id_for_label }}').val(data.start_date);
            $('#{{ form.end_date.id_for_label }}').val(data.end_date);
          },
          error: function() {
            console.error('Không thể lấy thông tin lịch trực.');
          }
        });
      }
    });
  });
</script>
{% endblock %}
