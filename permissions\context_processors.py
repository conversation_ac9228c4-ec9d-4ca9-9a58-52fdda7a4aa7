from .utils import get_user_permission_codes

def user_permissions(request):
    """
    Context processor để cung cấp thông tin về quyền của người dùng (feature-based)
    """
    # Khởi tạo context với tất cả quyền là False
    context = {}

    # Nếu người dùng đã đăng nhập
    if request.user.is_authenticated:
        # <PERSON><PERSON><PERSON> tất cả mã quyền của người dùng từ feature-based permission system
        user_permission_codes = get_user_permission_codes(request.user)

        # Thêm tất cả quyền vào context
        context['user_permissions'] = user_permission_codes
        context['user_permission_codes'] = user_permission_codes

        # Kiểm tra quyền cho từng chức năng (feature-based)
        # Quản lý người dùng
        context['user_has_user_management'] = any(code in user_permission_codes for code in [
            'users.view', 'users.add', 'users.edit', 'users.delete'
        ])

        # Qu<PERSON>n lý thiết bị
        context['user_has_device_management'] = any(code in user_permission_codes for code in [
            'devices.view', 'devices.add', 'devices.edit', 'devices.delete'
        ])

        # Quản lý danh mục
        context['user_has_category_management'] = any(code in user_permission_codes for code in [
            'departments.view', 'departments.add', 'departments.edit', 'departments.delete',
            'danhmuc130.view', 'danhmuc130.add', 'danhmuc130.edit', 'danhmuc130.delete',
            'danhmucbv.view', 'danhmucbv.add', 'danhmucbv.edit', 'danhmucbv.delete'
        ])

        # Email nội bộ
        context['user_has_email_access'] = any(code in user_permission_codes for code in [
            'emails.view', 'emails.send', 'emails.compose', 'emails.delete', 'emails.manage_folders'
        ])

        # Luyện tập gõ chữ
        context['user_has_typing_access'] = any(code in user_permission_codes for code in [
            'typingpractice.view', 'typingpractice.add', 'typingpractice.edit',
            'typingpractice.delete', 'typingpractice.take_exam', 'typingpractice.manage'
        ])

        # Giám sát hoạt động
        context['user_has_monitoring_access'] = any(code in user_permission_codes for code in [
            'devices.monitor', 'system.view_logs'
        ])

        # Lịch trực
        context['user_has_schedule_access'] = any(code in user_permission_codes for code in [
            'schedules.view', 'schedules.add', 'schedules.edit', 'schedules.delete',
            'schedules.assign', 'schedules.view_assignments'
        ])

        # Báo cáo thống kê
        context['user_has_report_access'] = any(code in user_permission_codes for code in [
            'reports.view', 'reports.create', 'reports.edit', 'reports.delete',
            'reports.export', 'reports.schedule'
        ])

        # Quản trị hệ thống
        context['user_has_system_admin'] = any(code in user_permission_codes for code in [
            'permissions.view', 'permissions.add', 'permissions.edit', 'permissions.delete',
            'system.manage', 'system.monitor', 'system.full_access'
        ])

        # Hồ sơ bệnh án
        context['user_has_hoso_access'] = any(code in user_permission_codes for code in [
            'hoso.view', 'hoso.add', 'hoso.edit', 'hoso.delete', 'hoso.import', 'hoso.export', 'hoso.approve'
        ])

        # XML 4750
        context['user_has_xml4750_access'] = any(code in user_permission_codes for code in [
            'xml4750.view', 'xml4750.import', 'xml4750.export', 'xml4750.validate', 'xml4750.edit', 'xml4750.delete'
        ])

        # Nếu có bất kỳ quyền quản trị nào
        context['user_has_admin_access'] = any([
            context['user_has_user_management'],
            context['user_has_device_management'],
            context['user_has_category_management'],
            context['user_has_email_access'],
            context['user_has_typing_access'],
            context['user_has_monitoring_access'],
            context['user_has_schedule_access'],
            context['user_has_report_access'],
            context['user_has_system_admin'],
            context['user_has_hoso_access'],
            context['user_has_xml4750_access']
        ])
    else:
        # Nếu người dùng chưa đăng nhập, đặt tất cả quyền là False
        context['user_permissions'] = set()
        context['user_permission_codes'] = set()
        context['user_has_user_management'] = False
        context['user_has_device_management'] = False
        context['user_has_category_management'] = False
        context['user_has_email_access'] = False
        context['user_has_typing_access'] = False
        context['user_has_monitoring_access'] = False
        context['user_has_schedule_access'] = False
        context['user_has_report_access'] = False
        context['user_has_system_admin'] = False
        context['user_has_admin_access'] = False
        context['user_has_hoso_access'] = False
        context['user_has_xml4750_access'] = False

    return context
