{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} phân công lịch trực{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} phân công lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:assignment_list' %}">Phân công lịch trực</a></li>
          <li class="breadcrumb-item active">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %}</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} phân công lịch trực</h3>
          </div>
          
          <form method="post" action="{% if is_edit %}{% url 'schedules:assignment_edit' assignment.id %}{% else %}{% url 'schedules:assignment_create' %}{% endif %}">
            {% csrf_token %}
            <div class="card-body">
              {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
              
              <div class="form-group">
                <label for="{{ form.schedule.id_for_label }}">Lịch trực <span class="text-danger">*</span></label>
                {{ form.schedule }}
                {% if form.schedule.errors %}
                <div class="text-danger">
                  {% for error in form.schedule.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.user.id_for_label }}">Nhân viên <span class="text-danger">*</span></label>
                {{ form.user }}
                {% if form.user.errors %}
                <div class="text-danger">
                  {% for error in form.user.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.shift.id_for_label }}">Ca trực <span class="text-danger">*</span></label>
                {{ form.shift }}
                {% if form.shift.errors %}
                <div class="text-danger">
                  {% for error in form.shift.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.date.id_for_label }}">Ngày trực <span class="text-danger">*</span></label>
                {{ form.date }}
                {% if form.date.errors %}
                <div class="text-danger">
                  {% for error in form.date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">Ngày phải nằm trong khoảng thời gian của lịch trực.</small>
              </div>
              
              <div class="form-group">
                <label for="{{ form.notes.id_for_label }}">Ghi chú</label>
                {{ form.notes }}
                {% if form.notes.errors %}
                <div class="text-danger">
                  {% for error in form.notes.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <div class="custom-control custom-checkbox">
                  {{ form.is_confirmed }}
                  <label class="custom-control-label" for="{{ form.is_confirmed.id_for_label }}">Đã xác nhận</label>
                </div>
                {% if form.is_confirmed.errors %}
                <div class="text-danger">
                  {% for error in form.is_confirmed.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              {% if is_edit %}
              <div class="form-group">
                <div class="custom-control custom-checkbox">
                  {{ form.is_completed }}
                  <label class="custom-control-label" for="{{ form.is_completed.id_for_label }}">Đã hoàn thành</label>
                </div>
                {% if form.is_completed.errors %}
                <div class="text-danger">
                  {% for error in form.is_completed.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              {% endif %}
            </div>
            
            <div class="card-footer">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Lưu
              </button>
              <a href="{% if is_edit %}{% url 'schedules:schedule_detail' assignment.schedule.id %}{% else %}{% url 'schedules:assignment_list' %}{% endif %}" class="btn btn-default">
                <i class="fas fa-times"></i> Hủy
              </a>
              {% if is_edit %}
              <button type="button" class="btn btn-danger float-right btn-delete-assignment" data-id="{{ assignment.id }}">
                <i class="fas fa-trash"></i> Xóa
              </button>
              {% endif %}
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });
    
    // Xử lý khi thay đổi lịch trực
    $('#{{ form.schedule.id_for_label }}').on('change', function() {
      const scheduleId = $(this).val();
      
      if (scheduleId) {
        // Lấy danh sách nhân viên theo khoa/phòng ban của lịch trực
        $.ajax({
          url: "{% url 'schedules:get_users_by_department' %}",
          type: 'GET',
          data: {
            'department_id': $('#{{ form.schedule.id_for_label }} option:selected').data('department-id')
          },
          dataType: 'json',
          success: function(data) {
            // Cập nhật danh sách nhân viên
            const userSelect = $('#{{ form.user.id_for_label }}');
            userSelect.empty();
            
            $.each(data.users, function(index, user) {
              userSelect.append(new Option(user.name, user.id));
            });
            
            // Trigger change để cập nhật Select2
            userSelect.trigger('change');
          },
          error: function() {
            console.error('Không thể lấy danh sách nhân viên.');
          }
        });
      }
    });
    
    // Xử lý xóa phân công
    $('.btn-delete-assignment').on('click', function() {
      const assignmentId = $(this).data('id');
      
      // Lấy thông tin phân công
      $.ajax({
        url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa phân công',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa phân công cho <strong>${data.user}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Lịch trực: ${data.schedule}</li>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Ca trực: ${data.shift}</li>
                  <li>Ngày: ${data.date}</li>
                  <li>Trạng thái: ${data.is_confirmed ? 'Đã xác nhận' : 'Chưa xác nhận'}</li>
                </ul>
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Chuyển hướng về trang chi tiết lịch trực
                    window.location.href = "{% url 'schedules:schedule_detail' 0 %}".replace('0', data.schedule_id);
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa phân công.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }
                  
                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin phân công.',
            icon: 'error'
          });
        }
      });
    });
  });
</script>
{% endblock %}
