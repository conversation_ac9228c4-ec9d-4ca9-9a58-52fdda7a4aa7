{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} ca trực{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} ca trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:shift_list' %}">Ca trực</a></li>
          <li class="breadcrumb-item active">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %}</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} ca trực</h3>
          </div>
          
          <form method="post" action="{% if is_edit %}{% url 'schedules:shift_edit' shift.id %}{% else %}{% url 'schedules:shift_create' %}{% endif %}">
            {% csrf_token %}
            <div class="card-body">
              {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
              
              <div class="form-group">
                <label for="{{ form.name.id_for_label }}">Tên ca trực <span class="text-danger">*</span></label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="text-danger">
                  {% for error in form.name.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.start_time.id_for_label }}">Thời gian bắt đầu <span class="text-danger">*</span></label>
                    {{ form.start_time }}
                    {% if form.start_time.errors %}
                    <div class="text-danger">
                      {% for error in form.start_time.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.end_time.id_for_label }}">Thời gian kết thúc <span class="text-danger">*</span></label>
                    {{ form.end_time }}
                    {% if form.end_time.errors %}
                    <div class="text-danger">
                      {% for error in form.end_time.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label for="{{ form.description.id_for_label }}">Mô tả</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="text-danger">
                  {% for error in form.description.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.color.id_for_label }}">Màu hiển thị</label>
                {{ form.color }}
                {% if form.color.errors %}
                <div class="text-danger">
                  {% for error in form.color.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              {% if is_edit and assignment_count > 0 %}
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Ca trực này đang được sử dụng trong {{ assignment_count }} phân công. Việc thay đổi thông tin có thể ảnh hưởng đến các phân công hiện có.
              </div>
              {% endif %}
            </div>
            
            <div class="card-footer">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Lưu
              </button>
              <a href="{% url 'schedules:shift_list' %}" class="btn btn-default">
                <i class="fas fa-times"></i> Hủy
              </a>
              {% if is_edit and assignment_count == 0 %}
              <a href="{% url 'schedules:shift_delete' shift.id %}" class="btn btn-danger float-right">
                <i class="fas fa-trash"></i> Xóa
              </a>
              {% endif %}
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    // Hiển thị màu sắc khi thay đổi
    $('#{{ form.color.id_for_label }}').on('change', function() {
      $(this).css('background-color', $(this).val());
    });
    
    // Hiển thị màu sắc ban đầu
    $('#{{ form.color.id_for_label }}').css('background-color', $('#{{ form.color.id_for_label }}').val());
  });
</script>
{% endblock %}
