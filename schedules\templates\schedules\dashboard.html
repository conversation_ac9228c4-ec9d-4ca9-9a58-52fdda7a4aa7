{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Lịch trực - Dashboard{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Dashboard Lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item active">Lịch trực</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <!-- Thông tin tổng quan -->
    <div class="row">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3>{{ active_schedules.count }}</h3>
            <p>Lịch tr<PERSON><PERSON> đang diễn ra</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-check"></i>
          </div>
          <a href="{% url 'schedules:schedule_list' %}?status=active" class="small-box-footer">
            Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>{{ upcoming_schedules.count }}</h3>
            <p>Lịch trực sắp tới</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <a href="{% url 'schedules:schedule_list' %}?status=upcoming" class="small-box-footer">
            Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3>{{ user_assignments.count }}</h3>
            <p>Ca trực của tôi</p>
          </div>
          <div class="icon">
            <i class="fas fa-user-clock"></i>
          </div>
          <a href="{% url 'schedules:my_schedule' %}" class="small-box-footer">
            Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
          <div class="inner">
            <h3>{{ unread_count }}</h3>
            <p>Thông báo chưa đọc</p>
          </div>
          <div class="icon">
            <i class="fas fa-bell"></i>
          </div>
          <a href="{% url 'schedules:my_schedule' %}?mark_read=1" class="small-box-footer">
            Đánh dấu đã đọc <i class="fas fa-check-circle"></i>
          </a>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Lịch trực đang diễn ra -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-calendar-check mr-1"></i>
              Lịch trực đang diễn ra
            </h3>
          </div>
          <div class="card-body">
            {% if active_schedules %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Tiêu đề</th>
                    <th>Khoa/Phòng ban</th>
                    <th>Thời gian</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for schedule in active_schedules %}
                  <tr>
                    <td>{{ schedule.title }}</td>
                    <td>{{ schedule.department.name }}</td>
                    <td>{{ schedule.start_date|date:"d/m/Y" }} - {{ schedule.end_date|date:"d/m/Y" }}</td>
                    <td>
                      <a href="{% url 'schedules:schedule_detail' schedule.id %}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <p class="text-center">Không có lịch trực nào đang diễn ra.</p>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Lịch trực sắp tới -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-calendar-alt mr-1"></i>
              Lịch trực sắp tới
            </h3>
          </div>
          <div class="card-body">
            {% if upcoming_schedules %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Tiêu đề</th>
                    <th>Khoa/Phòng ban</th>
                    <th>Thời gian</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for schedule in upcoming_schedules %}
                  <tr>
                    <td>{{ schedule.title }}</td>
                    <td>{{ schedule.department.name }}</td>
                    <td>{{ schedule.start_date|date:"d/m/Y" }} - {{ schedule.end_date|date:"d/m/Y" }}</td>
                    <td>
                      <a href="{% url 'schedules:schedule_detail' schedule.id %}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <p class="text-center">Không có lịch trực nào sắp tới.</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Ca trực của tôi -->
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-user-clock mr-1"></i>
              Ca trực của tôi
            </h3>
            <div class="card-tools">
              <a href="{% url 'schedules:my_schedule' %}" class="btn btn-sm btn-primary">
                Xem tất cả
              </a>
            </div>
          </div>
          <div class="card-body">
            {% if user_assignments %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Ngày</th>
                    <th>Ca trực</th>
                    <th>Lịch trực</th>
                    <th>Khoa/Phòng ban</th>
                    <th>Trạng thái</th>
                  </tr>
                </thead>
                <tbody>
                  {% for assignment in user_assignments %}
                  <tr>
                    <td>{{ assignment.date|date:"d/m/Y" }}</td>
                    <td>{{ assignment.shift.name }} ({{ assignment.shift.start_time|time:"H:i" }} - {{ assignment.shift.end_time|time:"H:i" }})</td>
                    <td>{{ assignment.schedule.title }}</td>
                    <td>{{ assignment.schedule.department.name }}</td>
                    <td>
                      {% if assignment.is_completed %}
                      <span class="badge badge-success">Đã hoàn thành</span>
                      {% elif assignment.is_confirmed %}
                      <span class="badge badge-primary">Đã xác nhận</span>
                      {% else %}
                      <span class="badge badge-warning">Chưa xác nhận</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <p class="text-center">Bạn không có ca trực nào sắp tới.</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
  $(function () {
    // Các xử lý JavaScript nếu cần
  });
</script>
{% endblock %}
