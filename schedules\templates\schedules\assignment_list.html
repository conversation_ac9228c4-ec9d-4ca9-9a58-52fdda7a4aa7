{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}<PERSON>h sách phân công lịch trực{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<style>
  .status-badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
  }
  .status-confirmed {
    background-color: #28a745;
    color: white;
  }
  .status-pending {
    background-color: #ffc107;
    color: #343a40;
  }
  .status-completed {
    background-color: #6c757d;
    color: white;
  }
</style>
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Danh sách phân công lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item active">Phân công lịch trực</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Danh sách phân công lịch trực</h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          
          <div class="card-body">
            <!-- Bộ lọc -->
            <div class="row mb-3">
              <div class="col-md-12">
                <form method="get" class="form-inline">
                  <div class="form-group mr-2 mb-2">
                    <input type="text" name="search" class="form-control" placeholder="Tìm kiếm..." value="{{ search_query }}">
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <select name="schedule" class="form-control select2">
                      <option value="">-- Tất cả lịch trực --</option>
                      {% for schedule in schedules %}
                      <option value="{{ schedule.id }}" {% if schedule_id|stringformat:"s" == schedule.id|stringformat:"s" %}selected{% endif %}>{{ schedule.title }}</option>
                      {% endfor %}
                    </select>
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <select name="department" class="form-control select2">
                      <option value="">-- Tất cả khoa/phòng ban --</option>
                      {% for dept in departments %}
                      <option value="{{ dept.id }}" {% if department_id|stringformat:"s" == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <select name="status" class="form-control">
                      <option value="">-- Tất cả trạng thái --</option>
                      <option value="completed" {% if status == 'completed' %}selected{% endif %}>Đã hoàn thành</option>
                      <option value="confirmed" {% if status == 'confirmed' %}selected{% endif %}>Đã xác nhận</option>
                      <option value="pending" {% if status == 'pending' %}selected{% endif %}>Chưa xác nhận</option>
                    </select>
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <input type="date" name="start_date" class="form-control" placeholder="Từ ngày" value="{{ start_date|date:'Y-m-d' }}">
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <input type="date" name="end_date" class="form-control" placeholder="Đến ngày" value="{{ end_date|date:'Y-m-d' }}">
                  </div>
                  
                  <button type="submit" class="btn btn-primary mb-2">
                    <i class="fas fa-search"></i> Lọc
                  </button>
                  
                  <a href="{% url 'schedules:assignment_list' %}" class="btn btn-default mb-2 ml-2">
                    <i class="fas fa-sync"></i> Đặt lại
                  </a>
                </form>
              </div>
            </div>
            
            <div class="mb-3">
              <a href="{% url 'schedules:assignment_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm phân công mới
              </a>
              <a href="{% url 'schedules:bulk_assignment_create' %}" class="btn btn-success">
                <i class="fas fa-users"></i> Phân công hàng loạt
              </a>
            </div>
            
            {% if assignments %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px">#</th>
                    <th>Lịch trực</th>
                    <th>Khoa/Phòng ban</th>
                    <th>Nhân viên</th>
                    <th>Ca trực</th>
                    <th>Ngày</th>
                    <th>Trạng thái</th>
                    <th>Ghi chú</th>
                    <th style="width: 120px">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for assignment in assignments %}
                  <tr {% if assignment.date == today %}class="table-warning"{% endif %}>
                    <td>{{ forloop.counter }}</td>
                    <td>
                      <a href="{% url 'schedules:schedule_detail' assignment.schedule.id %}">
                        {{ assignment.schedule.title }}
                      </a>
                    </td>
                    <td>{{ assignment.schedule.department.name }}</td>
                    <td>{{ assignment.user.get_full_name }}</td>
                    <td>{{ assignment.shift.name }}<br><small>{{ assignment.shift.start_time|time:"H:i" }} - {{ assignment.shift.end_time|time:"H:i" }}</small></td>
                    <td>{{ assignment.date|date:"d/m/Y" }}</td>
                    <td>
                      {% if assignment.is_completed %}
                      <span class="status-badge status-completed">Hoàn thành</span>
                      {% elif assignment.is_confirmed %}
                      <span class="status-badge status-confirmed">Đã xác nhận</span>
                      {% else %}
                      <span class="status-badge status-pending">Chưa xác nhận</span>
                      {% endif %}
                    </td>
                    <td>{{ assignment.notes|default:"Không có ghi chú" }}</td>
                    <td>
                      <div class="btn-group">
                        <a href="{% url 'schedules:assignment_edit' assignment.id %}" class="btn btn-sm btn-primary" title="Chỉnh sửa">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-danger btn-delete-assignment" data-id="{{ assignment.id }}" title="Xóa">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            
            {% if assignments.has_other_pages %}
            <div class="pagination mt-3">
              <ul class="pagination">
                {% if assignments.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if schedule_id %}&schedule={{ schedule_id }}{% endif %}{% if department_id %}&department={{ department_id }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if start_date %}&start_date={{ start_date|date:'Y-m-d' }}{% endif %}{% if end_date %}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}">&laquo; Đầu</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ assignments.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if schedule_id %}&schedule={{ schedule_id }}{% endif %}{% if department_id %}&department={{ department_id }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if start_date %}&start_date={{ start_date|date:'Y-m-d' }}{% endif %}{% if end_date %}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}">Trước</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&laquo; Đầu</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Trước</span>
                </li>
                {% endif %}
                
                {% for i in assignments.paginator.page_range %}
                  {% if assignments.number == i %}
                  <li class="page-item active">
                    <span class="page-link">{{ i }}</span>
                  </li>
                  {% elif i > assignments.number|add:'-3' and i < assignments.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if schedule_id %}&schedule={{ schedule_id }}{% endif %}{% if department_id %}&department={{ department_id }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if start_date %}&start_date={{ start_date|date:'Y-m-d' }}{% endif %}{% if end_date %}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}">{{ i }}</a>
                  </li>
                  {% endif %}
                {% endfor %}
                
                {% if assignments.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ assignments.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if schedule_id %}&schedule={{ schedule_id }}{% endif %}{% if department_id %}&department={{ department_id }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if start_date %}&start_date={{ start_date|date:'Y-m-d' }}{% endif %}{% if end_date %}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}">Tiếp</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ assignments.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if schedule_id %}&schedule={{ schedule_id }}{% endif %}{% if department_id %}&department={{ department_id }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if start_date %}&start_date={{ start_date|date:'Y-m-d' }}{% endif %}{% if end_date %}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}">Cuối &raquo;</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">Tiếp</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Cuối &raquo;</span>
                </li>
                {% endif %}
              </ul>
            </div>
            {% endif %}
            
            {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i> Không có phân công lịch trực nào.
              <a href="{% url 'schedules:assignment_create' %}" class="alert-link">Thêm phân công mới</a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });
    
    // Xử lý xóa phân công bằng SweetAlert2
    $('.btn-delete-assignment').on('click', function() {
      const assignmentId = $(this).data('id');
      
      // Lấy thông tin phân công
      $.ajax({
        url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa phân công',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa phân công cho <strong>${data.user}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Lịch trực: ${data.schedule}</li>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Ca trực: ${data.shift}</li>
                  <li>Ngày: ${data.date}</li>
                  <li>Trạng thái: ${data.is_confirmed ? 'Đã xác nhận' : 'Chưa xác nhận'}</li>
                </ul>
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Reload trang
                    window.location.reload();
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa phân công.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }
                  
                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin phân công.',
            icon: 'error'
          });
        }
      });
    });
  });
</script>
{% endblock %}
