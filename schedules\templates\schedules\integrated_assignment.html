{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}Ph<PERSON> công lịch trực tổng hợp{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/daterangepicker/daterangepicker.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.css' %}">
<style>
  .schedule-table {
    width: 100%;
    border-collapse: collapse;
  }

  .schedule-table th, .schedule-table td {
    border: 1px solid #dee2e6;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
  }

  .schedule-table th {
    background-color: #f4f6f9;
    font-weight: bold;
  }

  .schedule-date {
    font-weight: bold;
    background-color: #f8f9fa;
    width: 150px;
  }

  .schedule-cell {
    min-width: 200px;
    height: auto;
    position: relative;
    padding: 10px !important;
  }

  .schedule-cell select {
    width: 100%;
  }

  .schedule-cell .assigned-staff {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    position: relative;
    border-left: 3px solid #007bff;
  }

  .schedule-cell .assigned-staff .remove-staff {
    cursor: pointer;
    color: #dc3545;
  }

  .schedule-cell .assigned-staff .staff-name {
    font-weight: bold;
  }

  .weekend {
    background-color: #fff3cd;
  }

  .today {
    background-color: #d4edda;
  }

  .shift-header {
    background-color: #007bff;
    color: white;
    padding: 8px;
    border-radius: 4px 4px 0 0;
  }

  .shift-time {
    font-size: 0.8rem;
    display: block;
    margin-top: 3px;
  }

  .status-badge {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    display: inline-block;
    margin-top: 5px;
  }

  .status-confirmed {
    background-color: #28a745;
    color: white;
  }

  .status-pending {
    background-color: #ffc107;
    color: #343a40;
  }

  .status-completed {
    background-color: #6c757d;
    color: white;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .staff-select-container {
    margin-top: 10px;
    border-top: 1px dashed #ccc;
    padding-top: 10px;
  }

  .assigned-staff-container {
    max-height: 300px;
    overflow-y: auto;
  }

  /* Hiển thị ngày trong tuần */
  .schedule-date small {
    display: block;
    margin-top: 5px;
    font-size: 0.85em;
  }

  /* SweetAlert2 custom styles */
  .swal2-popup {
    font-size: 1rem;
  }

  .swal2-title {
    font-size: 1.5rem;
  }

  .swal2-content {
    font-size: 1rem;
  }

  .swal-wide {
    width: 500px !important;
  }

  .btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    box-shadow: none;
  }

  .btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
  }

  /* Shift info styles */
  .shift-info {
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
  }

  .shift-info .badge {
    font-size: 0.9rem;
    padding: 5px 10px;
  }

  /* Holiday styles */
  .holiday-toggle {
    margin-left: 5px;
  }

  .holiday-toggle .custom-control-label {
    cursor: pointer;
  }

  .holiday-toggle .custom-control-label i {
    font-size: 1rem;
  }

  .holiday-toggle .custom-control-input:checked ~ .custom-control-label i {
    color: #ffc107 !important;
  }

  .holiday-date {
    background-color: #fff8e1 !important;
  }

  .holiday-date .schedule-date {
    background-color: #fff3cd !important;
    border-left: 3px solid #ffc107 !important;
  }

  .holiday-date .badge-warning {
    background-color: #ffc107;
    color: #212529;
    font-weight: normal;
    font-size: 0.75rem;
  }

  /* Compact cell styles */
  .schedule-cell {
    padding: 6px !important;
  }

  .shift-info {
    margin-bottom: 5px !important;
    padding-bottom: 3px !important;
  }

  .shift-info .badge {
    font-size: 0.8rem !important;
    padding: 3px 8px !important;
  }

  .assigned-staff {
    padding: 4px 8px !important;
    margin-bottom: 4px !important;
    border-radius: 3px !important;
    background-color: #e9f7fe !important;
    border-left: 3px solid #007bff !important;
  }

  .staff-name {
    font-size: 0.85rem;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500 !important;
  }

  .remove-staff {
    color: #dc3545;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .remove-staff:hover {
    opacity: 1;
  }

  /* Selector dropdown */
  .staff-select-container {
    margin-top: 5px !important;
    padding-top: 5px !important;
  }

  .select2-container--bootstrap4 .select2-selection {
    font-size: 0.85rem !important;
    height: calc(1.8rem + 2px) !important;
  }

  .btn-add-staff {
    padding: 3px 8px !important;
    font-size: 0.85rem !important;
  }
</style>
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Phân công lịch trực tổng hợp</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item active">Phân công tổng hợp</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <!-- Card điều khiển -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Thiết lập phân công</h3>
      </div>
      <div class="card-body">
        <form id="schedule-form" method="post">
          {% csrf_token %}
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label>Khoa/Phòng ban</label>
                <select id="department" name="department" class="form-control select2" required>
                  <option value="">-- Chọn khoa/phòng ban --</option>
                  {% for dept in departments %}
                  <option value="{{ dept.id }}" {% if department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>Khoảng thời gian</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <i class="far fa-calendar-alt"></i>
                    </span>
                  </div>
                  <input type="text" class="form-control" id="date-range" name="date_range" required>
                </div>
              </div>
            </div>

          </div>
          <div class="row">
            <div class="col-md-12">
              <button type="button" id="btn-load-schedule" class="btn btn-primary">
                <i class="fas fa-sync"></i> Tải lịch trực
              </button>
              <button type="button" id="btn-save-schedule" class="btn btn-success" disabled>
                <i class="fas fa-save"></i> Lưu phân công
              </button>

            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Card lịch trực -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Bảng phân công lịch trực</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" data-card-widget="collapse">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <div id="schedule-container">
          <div class="text-center p-5">
            <i class="fas fa-calendar-alt fa-4x text-muted mb-3"></i>
            <h4>Chọn khoa/phòng ban và khoảng thời gian để tải lịch trực</h4>
            <p class="text-muted">Bảng phân công sẽ hiển thị ở đây sau khi bạn tải lịch trực</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Template cho bảng phân công -->
<script type="text/template" id="schedule-template">
  {% verbatim %}
  <div class="table-responsive">
    <table class="table table-bordered schedule-table">
      <thead>
        <tr>
          <th style="width: 150px;">Ngày</th>
          {{#shifts}}
          <th>
            <div class="shift-header">
              {{name}}
              <span class="shift-time">{{start_time}} - {{end_time}}</span>
            </div>
          </th>
          {{/shifts}}
        </tr>
      </thead>
      <tbody>
        {{#dates}}
        <tr class="{{#is_weekend}}weekend{{/is_weekend}} {{#is_today}}today{{/is_today}}">
          <td class="schedule-date">
            <div class="d-flex flex-column">
              <div class="d-flex justify-content-between align-items-center">
                <span class="font-weight-bold">{{formatted_date}}</span>
                <div class="holiday-toggle" data-date="{{date}}">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input holiday-checkbox" id="holiday-{{date}}" {{#is_holiday}}checked{{/is_holiday}}>
                    <label class="custom-control-label" for="holiday-{{date}}" title="Đánh dấu là ngày lễ"><i class="fas fa-star text-warning"></i></label>
                  </div>
                </div>
              </div>
              <small class="text-muted">{{day_of_week}}</small>
              {{#is_holiday}}<span class="badge badge-warning mt-1">Ngày lễ</span>{{/is_holiday}}
            </div>
          </td>
          {{#shifts}}
          <td class="schedule-cell" data-date="{{date}}" data-shift="{{id}}">
            <div class="assigned-staff-container">
              {{#assignments}}
              <div class="assigned-staff" data-assignment-id="{{id}}" data-user-id="{{user_id}}">
                <div class="d-flex justify-content-between align-items-center">
                  <span class="staff-name">{{user_name}}</span>
                  <span class="remove-staff" title="Xóa phân công"><i class="fas fa-times"></i></span>
                </div>
              </div>
              {{/assignments}}
            </div>
            <div class="staff-select-container mt-2">
              <select class="staff-select form-control select2-staff">
                <option value="">-- Chọn nhân viên --</option>
                {{#staff}}
                <option value="{{id}}">{{name}}</option>
                {{/staff}}
              </select>
              <button type="button" class="btn btn-sm btn-primary btn-add-staff mt-1 w-100">
                <i class="fas fa-plus"></i> Thêm nhân viên
              </button>
            </div>
          </td>
          {{/shifts}}
        </tr>
        {{/dates}}
      </tbody>
    </table>
  </div>
  {% endverbatim %}
</script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/daterangepicker/daterangepicker.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script src="{% static 'js/mustache.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });

    // Khởi tạo DateRangePicker
    $('#date-range').daterangepicker({
      locale: {
        format: 'DD/MM/YYYY',
        applyLabel: 'Áp dụng',
        cancelLabel: 'Hủy',
        fromLabel: 'Từ',
        toLabel: 'Đến',
        customRangeLabel: 'Tùy chỉnh',
        weekLabel: 'T',
        daysOfWeek: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
        monthNames: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        firstDay: 1
      },
      startDate: moment().startOf('isoWeek'), // Thứ 2 của tuần hiện tại
      endDate: moment().endOf('isoWeek'), // Chủ nhật của tuần hiện tại
      ranges: {
        'Tuần này': [moment().startOf('isoWeek'), moment().endOf('isoWeek')], // Thứ 2 đến Chủ nhật
        'Tuần sau': [moment().add(1, 'week').startOf('isoWeek'), moment().add(1, 'week').endOf('isoWeek')], // Thứ 2 đến Chủ nhật tuần sau
        'Tháng này': [moment().startOf('month'), moment().endOf('month')],
        'Tháng sau': [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')]
      }
    });

    // Xử lý nút tải lịch trực
    $('#btn-load-schedule').on('click', function() {
      const department = $('#department').val();
      const dateRange = $('#date-range').val();

      if (!department || !dateRange) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi',
          text: 'Vui lòng điền đầy đủ thông tin khoa/phòng ban và khoảng thời gian.',
          confirmButtonText: 'OK',
          customClass: {
            confirmButton: 'btn btn-primary',
            popup: 'swal-wide'
          },
          buttonsStyling: false
        });
        return;
      }

      // Hiển thị loading
      $('#schedule-container').html('<div class="text-center p-5"><i class="fas fa-spinner fa-spin fa-3x"></i><p class="mt-3">Đang tải lịch trực...</p></div>');

      // Gọi API để lấy dữ liệu
      $.ajax({
        url: "{% url 'schedules:get_integrated_schedule_data' %}",
        type: 'GET',
        data: {
          department: department,
          date_range: dateRange
        },
        dataType: 'json',
        success: function(data) {
          // Render bảng phân công
          renderScheduleTable(data);

          // Kích hoạt nút lưu
          $('#btn-save-schedule').prop('disabled', false);
        },
        error: function(xhr) {
          let errorMessage = 'Đã xảy ra lỗi khi tải lịch trực.';
          if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
          }

          Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            text: errorMessage,
            confirmButtonText: 'OK',
            customClass: {
              confirmButton: 'btn btn-primary',
              popup: 'swal-wide'
            },
            buttonsStyling: false
          });

          // Hiển thị lại thông báo ban đầu
          $('#schedule-container').html('<div class="text-center p-5"><i class="fas fa-calendar-alt fa-4x text-muted mb-3"></i><h4>Chọn khoa/phòng ban và khoảng thời gian để tải lịch trực</h4><p class="text-muted">Bảng phân công sẽ hiển thị ở đây sau khi bạn tải lịch trực</p></div>');
        }
      });
    });

    // Xử lý nút lưu phân công
    $('#btn-save-schedule').on('click', function() {
      // Thu thập dữ liệu phân công
      const assignments = collectAssignments();

      // Kiểm tra xem có phân công nào không
      if (assignments.length === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Cảnh báo',
          text: 'Chưa có phân công nào được tạo. Vui lòng phân công ít nhất một nhân viên.',
          confirmButtonText: 'OK',
          customClass: {
            confirmButton: 'btn btn-primary',
            popup: 'swal-wide'
          },
          buttonsStyling: false
        });
        return;
      }

      // Hiển thị loading
      const loadingOverlay = $('<div class="loading-overlay"><i class="fas fa-spinner fa-spin fa-3x"></i></div>');
      $('body').append(loadingOverlay);

      // Log dữ liệu để debug
      console.log('Assignments to save:', assignments);

      // Gọi API để lưu phân công
      $.ajax({
        url: "{% url 'schedules:save_integrated_schedule' %}",
        type: 'POST',
        data: {
          'csrfmiddlewaretoken': '{{ csrf_token }}',
          'department': $('#department').val(),
          'date_range': $('#date-range').val(),
          'assignments': JSON.stringify(assignments)
        },
        dataType: 'json',
        success: function(response) {
          // Xóa loading
          loadingOverlay.remove();

          // Hiển thị thông báo thành công
          Swal.fire({
            icon: 'success',
            title: 'Thành công',
            text: response.message,
            confirmButtonText: 'Xem lịch trực',
            showCancelButton: true,
            cancelButtonText: 'Ở lại trang này',
            customClass: {
              confirmButton: 'btn btn-success mr-2',
              cancelButton: 'btn btn-secondary',
              popup: 'swal-wide'
            },
            buttonsStyling: false
          }).then((result) => {
            if (result.isConfirmed && response.schedule_id) {
              // Chuyển hướng đến trang chi tiết lịch trực
              window.location.href = "{% url 'schedules:schedule_detail' 0 %}".replace('0', response.schedule_id);
            }
          });
        },
        error: function(xhr) {
          // Xóa loading
          loadingOverlay.remove();

          console.error('Error response:', xhr);

          let errorMessage = 'Đã xảy ra lỗi khi lưu phân công.';
          let errorDetails = '';

          if (xhr.responseJSON) {
            if (xhr.responseJSON.message) {
              errorMessage = xhr.responseJSON.message;
            } else if (xhr.responseJSON.error) {
              errorMessage = xhr.responseJSON.error;
            }

            if (xhr.responseJSON.traceback) {
              errorDetails = xhr.responseJSON.traceback;
              console.error('Error traceback:', errorDetails);
            }
          }

          Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            html: `<p>${errorMessage}</p>
                  <details>
                    <summary>Chi tiết lỗi (dành cho kỹ thuật viên)</summary>
                    <pre style="text-align: left; max-height: 200px; overflow: auto;">${errorDetails || 'Không có thông tin chi tiết'}</pre>
                  </details>`,
            confirmButtonText: 'OK',
            customClass: {
              confirmButton: 'btn btn-primary',
              popup: 'swal-wide'
            },
            buttonsStyling: false
          });
        }
      });
    });

    // Hàm render bảng phân công
    function renderScheduleTable(data) {
      const template = $('#schedule-template').html();
      const rendered = Mustache.render(template, data);
      $('#schedule-container').html(rendered);

      // Khởi tạo Select2 cho các select nhân viên
      $('.select2-staff').select2({
        theme: 'bootstrap4',
        width: '100%'
      });

      // Xử lý nút thêm nhân viên
      $(document).off('click', '.btn-add-staff').on('click', '.btn-add-staff', function() {
        const cell = $(this).closest('.schedule-cell');
        const date = cell.data('date');
        const shiftId = cell.data('shift');
        const staffSelect = cell.find('.staff-select');
        const staffId = staffSelect.val();
        const staffName = staffSelect.find('option:selected').text();

        if (!staffId) {
          Swal.fire({
            icon: 'warning',
            title: 'Cảnh báo',
            text: 'Vui lòng chọn nhân viên.',
            confirmButtonText: 'OK',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
          return;
        }

        // Kiểm tra xem nhân viên đã được phân công vào ca trực này chưa
        const existingStaff = cell.find(`.assigned-staff[data-user-id="${staffId}"]`);
        if (existingStaff.length > 0) {
          Swal.fire({
            icon: 'warning',
            title: 'Cảnh báo',
            text: 'Nhân viên này đã được phân công vào ca trực này.',
            confirmButtonText: 'OK',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
          return;
        }

        // Thêm nhân viên vào cell
        const assignedStaffContainer = cell.find('.assigned-staff-container');
        const assignedStaff = $(`
          <div class="assigned-staff" data-temp-id="${Date.now()}" data-user-id="${staffId}" data-date="${date}" data-shift="${shiftId}">
            <div class="d-flex justify-content-between align-items-center">
              <span class="staff-name">${staffName}</span>
              <span class="remove-staff" title="Xóa phân công"><i class="fas fa-times"></i></span>
            </div>
          </div>
        `);
        assignedStaffContainer.append(assignedStaff);

        // Reset select
        staffSelect.val('').trigger('change');
      });

      // Xử lý nút xóa nhân viên
      $(document).off('click', '.remove-staff').on('click', '.remove-staff', function() {
        const assignedStaff = $(this).closest('.assigned-staff');
        const staffName = assignedStaff.find('.staff-name').text();

        // Nếu là phân công đã có trong DB (có assignment-id)
        if (assignedStaff.data('assignment-id')) {
          Swal.fire({
            icon: 'warning',
            title: 'Xác nhận xóa',
            text: `Bạn có chắc chắn muốn xóa phân công của "${staffName}" không?`,
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
              confirmButton: 'btn btn-danger mr-2',
              cancelButton: 'btn btn-secondary',
              popup: 'swal-wide'
            },
            buttonsStyling: false
          }).then((result) => {
            if (result.isConfirmed) {
              assignedStaff.remove();
            }
          });
        } else {
          // Nếu là phân công mới
          assignedStaff.remove();
        }
      });

      // Xử lý checkbox ngày lễ
      $(document).off('change', '.holiday-checkbox').on('change', '.holiday-checkbox', function() {
        const isChecked = $(this).is(':checked');
        const date = $(this).closest('.holiday-toggle').data('date');
        const row = $(this).closest('tr');
        const dayOfWeek = row.find('.schedule-date small').text();
        const formattedDate = row.find('.schedule-date .font-weight-bold').text();

        if (isChecked) {
          // Hiển thị modal để nhập thông tin ngày lễ
          Swal.fire({
            title: 'Thêm ngày lễ',
            html: `
              <form id="holiday-form">
                <div class="form-group">
                  <label for="holiday-name">Tên ngày lễ</label>
                  <input type="text" class="form-control" id="holiday-name" placeholder="Nhập tên ngày lễ">
                </div>
                <div class="form-group">
                  <label for="holiday-description">Mô tả (tùy chọn)</label>
                  <textarea class="form-control" id="holiday-description" rows="3"></textarea>
                </div>
              </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Lưu',
            cancelButtonText: 'Hủy',
            customClass: {
              confirmButton: 'btn btn-success mr-2',
              cancelButton: 'btn btn-secondary',
              popup: 'swal-wide'
            },
            buttonsStyling: false,
            preConfirm: () => {
              const name = document.getElementById('holiday-name').value;
              if (!name) {
                Swal.showValidationMessage('Vui lòng nhập tên ngày lễ');
                return false;
              }
              return {
                name: name,
                description: document.getElementById('holiday-description').value
              };
            }
          }).then((result) => {
            if (result.isConfirmed) {
              // Lưu thông tin ngày lễ
              $.ajax({
                url: "{% url 'schedules:save_holiday' %}",
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}',
                  'date': date,
                  'name': result.value.name,
                  'description': result.value.description
                },
                dataType: 'json',
                success: function(response) {
                  row.addClass('holiday-date');
                  // Cập nhật trạng thái ngày lễ cho tất cả các phân công trong ngày này
                  row.find('.assigned-staff').attr('data-is-holiday', 'true');

                  // Thêm badge ngày lễ
                  if (row.find('.badge-warning').length === 0) {
                    row.find('.schedule-date .d-flex.flex-column').append(`<span class="badge badge-warning mt-1">${result.value.name}</span>`);
                  } else {
                    row.find('.badge-warning').text(result.value.name);
                  }

                  // Hiển thị thông báo
                  const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                      toast.addEventListener('mouseenter', Swal.stopTimer)
                      toast.addEventListener('mouseleave', Swal.resumeTimer)
                    }
                  });

                  Toast.fire({
                    icon: 'success',
                    title: `Đã đánh dấu ${dayOfWeek} (${formattedDate}) là ngày lễ`
                  });
                },
                error: function(xhr) {
                  let errorMessage = 'Đã xảy ra lỗi khi lưu thông tin ngày lễ.';
                  if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                  }

                  Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: errorMessage,
                    confirmButtonText: 'OK',
                    customClass: {
                      confirmButton: 'btn btn-primary',
                      popup: 'swal-wide'
                    },
                    buttonsStyling: false
                  });

                  // Bỏ chọn checkbox
                  $(this).prop('checked', false);
                }
              });
            } else {
              // Bỏ chọn checkbox
              $(this).prop('checked', false);
            }
          });
        } else {
          // Xác nhận xóa ngày lễ
          Swal.fire({
            title: 'Xác nhận xóa',
            text: `Bạn có chắc chắn muốn bỏ đánh dấu ngày lễ cho ${dayOfWeek} (${formattedDate}) không?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy',
            customClass: {
              confirmButton: 'btn btn-danger mr-2',
              cancelButton: 'btn btn-secondary',
              popup: 'swal-wide'
            },
            buttonsStyling: false
          }).then((result) => {
            if (result.isConfirmed) {
              row.removeClass('holiday-date');
              // Cập nhật trạng thái ngày lễ cho tất cả các phân công trong ngày này
              row.find('.assigned-staff').attr('data-is-holiday', 'false');

              // Xóa badge ngày lễ
              row.find('.badge-warning').remove();

              // Hiển thị thông báo
              const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                  toast.addEventListener('mouseenter', Swal.stopTimer)
                  toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
              });

              Toast.fire({
                icon: 'success',
                title: `Đã bỏ đánh dấu ngày lễ cho ${dayOfWeek} (${formattedDate})`
              });
            } else {
              // Chọn lại checkbox
              $(this).prop('checked', true);
            }
          });
        }
      });
    }

    // Hàm thu thập dữ liệu phân công
    function collectAssignments() {
      const assignments = [];

      // Thu thập các phân công đã có trong DB
      $('.assigned-staff[data-assignment-id]').each(function() {
        const assignmentId = $(this).data('assignment-id');
        assignments.push({
          id: assignmentId,
          action: 'keep'
        });
      });

      // Thu thập các phân công mới
      $('.assigned-staff:not([data-assignment-id])').each(function() {
        const userId = $(this).data('user-id');
        const date = $(this).data('date');
        const shiftId = $(this).data('shift');
        const isHoliday = $(`#holiday-${date}`).is(':checked');

        assignments.push({
          user_id: userId,
          date: date,
          shift_id: shiftId,
          is_holiday: isHoliday,
          action: 'add'
        });
      });

      return assignments;
    }
  });
</script>
{% endblock %}
