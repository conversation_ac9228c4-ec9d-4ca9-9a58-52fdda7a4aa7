{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} lịch trực{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:schedule_list' %}">Danh sách lịch trực</a></li>
          <li class="breadcrumb-item active">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %}</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{% if is_edit %}Chỉnh sửa{% else %}Thêm mới{% endif %} lịch trực</h3>
          </div>
          
          <form method="post" action="{% if is_edit %}{% url 'schedules:schedule_edit' schedule.id %}{% else %}{% url 'schedules:schedule_create' %}{% endif %}">
            {% csrf_token %}
            <div class="card-body">
              {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
              {% endif %}
              
              <div class="form-group">
                <label for="{{ form.title.id_for_label }}">Tiêu đề <span class="text-danger">*</span></label>
                {{ form.title }}
                {% if form.title.errors %}
                <div class="text-danger">
                  {% for error in form.title.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <label for="{{ form.department.id_for_label }}">Khoa/Phòng ban <span class="text-danger">*</span></label>
                {{ form.department }}
                {% if form.department.errors %}
                <div class="text-danger">
                  {% for error in form.department.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.start_date.id_for_label }}">Ngày bắt đầu <span class="text-danger">*</span></label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="text-danger">
                      {% for error in form.start_date.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="{{ form.end_date.id_for_label }}">Ngày kết thúc <span class="text-danger">*</span></label>
                    {{ form.end_date }}
                    {% if form.end_date.errors %}
                    <div class="text-danger">
                      {% for error in form.end_date.errors %}
                        {{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label for="{{ form.description.id_for_label }}">Mô tả</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="text-danger">
                  {% for error in form.description.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              <div class="form-group">
                <div class="custom-control custom-checkbox">
                  {{ form.is_published }}
                  <label class="custom-control-label" for="{{ form.is_published.id_for_label }}">Công bố lịch trực</label>
                  <small class="form-text text-muted">Khi được công bố, lịch trực sẽ hiển thị cho tất cả người dùng trong khoa/phòng ban.</small>
                </div>
                {% if form.is_published.errors %}
                <div class="text-danger">
                  {% for error in form.is_published.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
                {% endif %}
              </div>
              
              {% if is_edit and assignment_count > 0 %}
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Lịch trực này đang có {{ assignment_count }} phân công. Việc thay đổi thông tin có thể ảnh hưởng đến các phân công hiện có.
              </div>
              {% endif %}
            </div>
            
            <div class="card-footer">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Lưu
              </button>
              <a href="{% if is_edit %}{% url 'schedules:schedule_detail' schedule.id %}{% else %}{% url 'schedules:schedule_list' %}{% endif %}" class="btn btn-default">
                <i class="fas fa-times"></i> Hủy
              </a>
              {% if is_edit and user.is_superuser or is_edit and user == schedule.created_by %}
              <button type="button" class="btn btn-danger float-right btn-delete-schedule" data-id="{{ schedule.id }}">
                <i class="fas fa-trash"></i> Xóa
              </button>
              {% endif %}
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });
    
    // Xử lý xóa lịch trực bằng SweetAlert2
    $('.btn-delete-schedule').on('click', function() {
      const scheduleId = $(this).data('id');
      
      // Lấy thông tin lịch trực
      $.ajax({
        url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa lịch trực',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa lịch trực <strong>${data.title}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Thời gian: ${data.start_date} - ${data.end_date}</li>
                  <li>Số phân công: ${data.assignment_count}</li>
                  <li>Người tạo: ${data.created_by}</li>
                </ul>
                ${data.assignment_count > 0 ? '<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Lịch trực này đã có phân công. Xóa lịch trực sẽ xóa tất cả các phân công liên quan.</p>' : ''}
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Chuyển hướng về trang danh sách
                    window.location.href = "{% url 'schedules:schedule_list' %}";
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa lịch trực.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }
                  
                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin lịch trực.',
            icon: 'error'
          });
        }
      });
    });
  });
</script>
{% endblock %}
