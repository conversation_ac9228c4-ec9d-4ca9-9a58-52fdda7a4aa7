{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}Lịch trực c<PERSON>a tôi{% endblock %}

{% block extra_css %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/daterangepicker/daterangepicker.css' %}">
<style>
  .schedule-table {
    width: 100%;
    border-collapse: collapse;
  }

  .schedule-table th, .schedule-table td {
    border: 1px solid #dee2e6;
    padding: 8px;
    vertical-align: top;
    text-align: center;
  }

  .schedule-table th {
    background-color: #f4f6f9;
  }

  .schedule-date {
    font-weight: bold;
    background-color: #f8f9fa;
  }

  .assignment-item {
    background-color: #f0f7ff;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 5px;
    border-left: 3px solid #007bff;
  }

  .assignment-item:last-child {
    margin-bottom: 0;
  }

  .staff-info {
    margin-bottom: 5px;
    text-align: left;
  }

  .assignment-notes {
    margin-top: 5px;
    color: #6c757d;
    border-top: 1px dashed #dee2e6;
    padding-top: 5px;
    text-align: left;
  }

  .status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 5px;
  }

  .status-completed {
    background-color: #28a745;
    color: white;
  }

  .status-confirmed {
    background-color: #17a2b8;
    color: white;
  }

  .status-pending {
    background-color: #ffc107;
    color: #212529;
  }

  .table-warning {
    background-color: #fff3cd;
  }

  /* Sửa lỗi CSS cho form filter */
  #schedule-filter-form .select2-container {
    width: 100% !important;
  }

  #schedule-filter-form .btn-group {
    display: flex;
  }

  #schedule-filter-form .btn {
    margin-right: 5px;
  }

  .daterangepicker {
    z-index: 1100 !important;
  }

  /* Sửa lỗi hiển thị form filter */
  #schedule-filter-form {
    width: 100%;
  }

  #schedule-filter-form .row {
    margin-right: 0;
    margin-left: 0;
    display: flex;
    flex-wrap: wrap;
  }

  #schedule-filter-form .col-md-3 {
    padding-right: 10px;
    padding-left: 10px;
  }

  /* Cơ bản cho Select2 */
  .select2-container--bootstrap4 .select2-selection--single {
    height: calc(2.25rem + 2px) !important;
  }

  .select2-container--bootstrap4 .select2-selection__rendered {
    line-height: 1.5 !important;
  }

  .select2-dropdown {
    border-color: #ced4da !important;
  }

  @media (max-width: 768px) {
    #schedule-filter-form .col-md-3 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Lịch trực của tôi</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item active">Lịch trực của tôi</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <!-- Thông tin tổng quan -->
    <div class="row">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3>{{ today_count }}</h3>
            <p>Ca trực hôm nay</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-day"></i>
          </div>
          <a href="?status=today" class="small-box-footer">
            Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>{{ upcoming_count }}</h3>
            <p>Ca trực sắp tới</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <a href="?status=upcoming" class="small-box-footer">
            Xem chi tiết <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>

      <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
          <div class="inner">
            <h3>{{ unread_count }}</h3>
            <p>Thông báo chưa đọc</p>
          </div>
          <div class="icon">
            <i class="fas fa-bell"></i>
          </div>
          <a href="?mark_read=1" class="small-box-footer">
            Đánh dấu đã đọc <i class="fas fa-check-circle"></i>
          </a>
        </div>
      </div>
    </div>

    <!-- Thông báo chưa đọc -->
    {% if unread_notifications %}
    <div class="row">
      <div class="col-md-12">
        <div class="card card-danger">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-bell mr-1"></i>
              Thông báo chưa đọc
            </h3>
            <div class="card-tools">
              <a href="?mark_read=1" class="btn btn-sm btn-outline-light">
                <i class="fas fa-check-double"></i> Đánh dấu tất cả đã đọc
              </a>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Lịch trực</th>
                    <th>Ca trực</th>
                    <th>Ngày</th>
                    <th>Thời gian thông báo</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for notification in unread_notifications %}
                  <tr>
                    <td>{{ notification.assignment.schedule.title }}</td>
                    <td>{{ notification.assignment.shift.name }} ({{ notification.assignment.shift.start_time|time:"H:i" }} - {{ notification.assignment.shift.end_time|time:"H:i" }})</td>
                    <td>{{ notification.assignment.date|date:"d/m/Y" }}</td>
                    <td>{{ notification.sent_at|date:"d/m/Y H:i" }}</td>
                    <td>
                      <a href="{% url 'schedules:schedule_detail' notification.assignment.schedule.id %}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i> Xem lịch trực
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Chọn lịch trực của khoa -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-calendar-alt mr-1"></i>
              Xem lịch trực
            </h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          <div class="card-body">
            <form method="get" id="schedule-filter-form">
              <div class="row">
                <div class="col-md-3 mb-2">
                  <label>Khoa/Phòng ban:</label>
                  <select name="department" id="department-select" class="form-control select2">
                    <option value="">-- Lịch trực của tôi --</option>
                    {% for department in departments %}
                    <option value="{{ department.id }}" {% if selected_department == department.id %}selected{% endif %}>{{ department.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-md-3 mb-2">
                  <label>Khoảng thời gian:</label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <i class="far fa-calendar-alt"></i>
                      </span>
                    </div>
                    <input type="text" class="form-control" id="date-range-picker" name="date_range"
                      value="{{ date_range_str|default:'' }}" placeholder="Chọn khoảng thời gian">
                  </div>
                </div>
                <div class="col-md-3 mb-2">
                  <label>Trạng thái:</label>
                  <select name="status" class="form-control select2">
                    <option value="">-- Tất cả --</option>
                    <option value="today" {% if status_filter == 'today' %}selected{% endif %}>Hôm nay</option>
                    <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>Sắp tới</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Đã hoàn thành</option>
                  </select>
                </div>
                <div class="col-md-3 mb-2">
                  <label>&nbsp;</label>
                  <div class="d-flex">
                    <button type="submit" class="btn btn-primary btn-block">
                      <i class="fas fa-filter"></i> Lọc
                    </button>
                    <a href="{% url 'schedules:my_schedule' %}" class="btn btn-default ml-2">
                      <i class="fas fa-sync"></i>
                    </a>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Phân công lịch trực -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-user-clock mr-1"></i>
              Phân công lịch trực
            </h3>
          </div>
          <div class="card-body">
            {% if assignments %}
            <div class="table-responsive">
              <table class="table table-bordered schedule-table">
                <thead>
                  <tr>
                    <th style="width: 150px">Ngày</th>
                    {% for shift in shifts %}
                    <th>{{ shift.name }}<br><small>{{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}</small></th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% for date in date_range %}
                  <tr data-date="{{ date|date:'Y-m-d' }}" {% if date == today %}class="table-warning"{% endif %}>
                    <td class="schedule-date">{{ date|date:"D, d/m/Y" }}</td>
                    {% for shift in shifts %}
                    <td>
                      {% with date_str=date|date:"Y-m-d" %}
                      {% with shift_id=shift.id|stringformat:"s" %}
                      {% with date_shift=date_str|add:"_"|add:shift_id %}
                      {% with assignments=schedule_data|get_assignment:date_shift %}
                      {% if assignments %}
                      {% for assignment in assignments %}
                      <div class="assignment-item">
                        <div class="staff-info">
                          <i class="fas fa-user-md mr-1"></i>
                          <strong>{{ assignment.user.get_full_name|default:assignment.user.username }}</strong>
                          {% if assignment.user == user %}<span class="badge badge-primary ml-1">Bạn</span>{% endif %}
                        </div>
                        {% if assignment.is_completed %}
                        <span class="status-badge status-completed">Hoàn thành</span>
                        {% elif assignment.is_confirmed %}
                        <span class="status-badge status-confirmed">Đã xác nhận</span>
                        {% else %}
                        <span class="status-badge status-pending">Chưa xác nhận</span>
                        {% endif %}
                        {% if assignment.notes %}
                        <div class="assignment-notes">
                          <small><i class="fas fa-sticky-note mr-1"></i> {{ assignment.notes }}</small>
                        </div>
                        {% endif %}
                      </div>
                      {% endfor %}
                      {% else %}
                      <span class="text-muted">Chưa có phân công</span>
                      {% endif %}
                      {% endwith %}
                      {% endwith %}
                      {% endwith %}
                      {% endwith %}
                    </td>
                    {% endfor %}
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i> Không có phân công nào cho lịch trực này.
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}



{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/daterangepicker/daterangepicker.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2 đơn giản
    $('.select2').select2({
      theme: 'bootstrap4'
    });

    // Khởi tạo Date Range Picker
    // Đặt firstDay = 1 để tuần bắt đầu từ thứ 2
    moment.locale('vi', {
      week: {
        dow: 1, // Thứ 2 là ngày đầu tuần
        doy: 4  // Tuần chứa ngày 4 tháng 1 là tuần đầu tiên của năm
      }
    });

    $('#date-range-picker').daterangepicker({
      locale: {
        format: 'DD/MM/YYYY',
        applyLabel: 'Áp dụng',
        cancelLabel: 'Hủy',
        fromLabel: 'Từ',
        toLabel: 'Đến',
        customRangeLabel: 'Tùy chỉnh',
        weekLabel: 'T',
        daysOfWeek: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
        monthNames: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
        firstDay: 1 // Thứ 2 là ngày đầu tuần
      },
      startDate: moment().startOf('isoWeek'), // isoWeek bắt đầu từ thứ 2
      endDate: moment().endOf('isoWeek'),     // isoWeek kết thúc vào chủ nhật
      ranges: {
        'Hôm nay': [moment(), moment()],
        'Tuần này': [moment().startOf('isoWeek'), moment().endOf('isoWeek')],
        'Tuần sau': [moment().add(1, 'week').startOf('isoWeek'), moment().add(1, 'week').endOf('isoWeek')],
        'Tháng này': [moment().startOf('month'), moment().endOf('month')],
        'Tháng sau': [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')]
      }
    });

    // Xử lý chọn khoa/phòng ban
    $('#department-select').on('change', function() {
      // Không tự động submit form khi chọn khoa/phòng ban
    });

    // Đánh dấu ngày hiện tại
    const today = new Date().toISOString().split('T')[0];
    $('tr[data-date="' + today + '"]').addClass('table-warning');
  });
</script>
{% endblock %}
