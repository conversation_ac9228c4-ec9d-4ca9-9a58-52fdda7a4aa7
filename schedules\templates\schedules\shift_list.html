{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}Danh sách ca trực{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Danh sách ca trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item active">Ca trực</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Danh sách ca trực</h3>
            <div class="card-tools">
              <form method="get" class="input-group input-group-sm" style="width: 250px;">
                <input type="text" name="search" class="form-control float-right" placeholder="Tìm kiếm..." value="{{ search_query }}">
                <div class="input-group-append">
                  <button type="submit" class="btn btn-default">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <div class="card-body">
            <div class="mb-3">
              <a href="{% url 'schedules:shift_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm ca trực mới
              </a>
            </div>

            {% if shifts %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px">#</th>
                    <th>Tên ca trực</th>
                    <th>Thời gian</th>
                    <th>Thời lượng</th>
                    <th>Màu hiển thị</th>
                    <th>Số phân công</th>
                    <th style="width: 100px">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for shift in shifts %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ shift.name }}</td>
                    <td>{{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}</td>
                    <td>{{ shift.duration }} giờ</td>
                    <td>
                      <span class="badge" style="background-color: {{ shift.color }}; color: #fff; padding: 5px 10px;">
                        {{ shift.color }}
                      </span>
                    </td>
                    <td>{{ shift_stats|get_item:shift.id|default:"0" }}</td>
                    <td>
                      <div class="btn-group">
                        <a href="{% url 'schedules:shift_edit' shift.id %}" class="btn btn-sm btn-info" title="Chỉnh sửa">
                          <i class="fas fa-edit"></i>
                        </a>
                        {% if shift_stats|get_item:shift.id|default:"0" == 0 %}
                        <button class="btn btn-sm btn-danger btn-delete-shift" data-id="{{ shift.id }}" title="Xóa">
                          <i class="fas fa-trash"></i>
                        </button>
                        {% else %}
                        <button class="btn btn-sm btn-secondary" disabled title="Không thể xóa vì đã có phân công">
                          <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            {% if shifts.has_other_pages %}
            <div class="pagination mt-3">
              <ul class="pagination">
                {% if shifts.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">&laquo; Đầu</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ shifts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Trước</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&laquo; Đầu</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Trước</span>
                </li>
                {% endif %}

                {% for i in shifts.paginator.page_range %}
                  {% if shifts.number == i %}
                  <li class="page-item active">
                    <span class="page-link">{{ i }}</span>
                  </li>
                  {% elif i > shifts.number|add:'-3' and i < shifts.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                  </li>
                  {% endif %}
                {% endfor %}

                {% if shifts.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ shifts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Tiếp</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ shifts.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">Cuối &raquo;</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">Tiếp</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Cuối &raquo;</span>
                </li>
                {% endif %}
              </ul>
            </div>
            {% endif %}

            {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i> Không có ca trực nào.
              <a href="{% url 'schedules:shift_create' %}" class="alert-link">Thêm ca trực mới</a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Xử lý xóa ca trực bằng SweetAlert2
    $('.btn-delete-shift').on('click', function() {
      const shiftId = $(this).data('id');

      // Lấy thông tin ca trực
      $.ajax({
        url: "{% url 'schedules:shift_delete' 0 %}".replace('0', shiftId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa ca trực',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa ca trực <strong>${data.name}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Thời gian: ${data.start_time} - ${data.end_time}</li>
                  <li>Thời lượng: ${data.duration} giờ</li>
                  <li>Màu hiển thị: <span class="badge" style="background-color: ${data.color}; color: #fff; padding: 5px 10px;">${data.color}</span></li>
                  <li>Mô tả: ${data.description}</li>
                </ul>
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:shift_delete' 0 %}".replace('0', shiftId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Reload trang
                    window.location.reload();
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa ca trực.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }

                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin ca trực.',
            icon: 'error'
          });
        }
      });
    });
  });
</script>
{% endblock %}
