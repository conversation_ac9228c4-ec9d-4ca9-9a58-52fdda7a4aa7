{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}Chi tiết lịch trực{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<style>
  .schedule-table {
    width: 100%;
    border-collapse: collapse;
  }

  .schedule-table th, .schedule-table td {
    border: 1px solid #dee2e6;
    padding: 8px;
    vertical-align: top;
    text-align: center;
  }

  .schedule-table th {
    background-color: #f4f6f9;
  }

  .schedule-date {
    font-weight: bold;
    background-color: #f8f9fa;
  }

  .assignment-item {
    background-color: #f0f7ff;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 5px;
    border-left: 3px solid #007bff;
  }

  .assignment-item:last-child {
    margin-bottom: 0;
  }

  .staff-info {
    margin-bottom: 5px;
    text-align: left;
  }

  .assignment-notes {
    margin-top: 5px;
    color: #6c757d;
    border-top: 1px dashed #dee2e6;
    padding-top: 5px;
    text-align: left;
  }

  .status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-left: 5px;
  }

  .status-completed {
    background-color: #28a745;
    color: white;
  }

  .status-confirmed {
    background-color: #17a2b8;
    color: white;
  }

  .status-pending {
    background-color: #ffc107;
    color: #212529;
  }
</style>
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Chi tiết lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:schedule_list' %}">Danh sách lịch trực</a></li>
          <li class="breadcrumb-item active">Chi tiết</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <!-- Thông tin lịch trực -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-calendar-alt mr-1"></i>
              Thông tin lịch trực
            </h3>
            <div class="card-tools">
              {% if can_edit %}
              <a href="{% url 'schedules:schedule_edit' schedule.id %}" class="btn btn-sm btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
              </a>
              {% endif %}
              {% if can_delete %}
              <button type="button" class="btn btn-sm btn-danger btn-delete-schedule" data-id="{{ schedule.id }}">
                <i class="fas fa-trash"></i> Xóa
              </button>
              {% endif %}
            </div>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <table class="table table-bordered">
                  <tr>
                    <th style="width: 200px">Tiêu đề</th>
                    <td>{{ schedule.title }}</td>
                  </tr>
                  <tr>
                    <th>Khoa/Phòng ban</th>
                    <td>{{ schedule.department.name }}</td>
                  </tr>
                  <tr>
                    <th>Thời gian</th>
                    <td>{{ schedule.start_date|date:"d/m/Y" }} - {{ schedule.end_date|date:"d/m/Y" }}</td>
                  </tr>
                  <tr>
                    <th>Trạng thái</th>
                    <td>
                      {% if schedule.status == 'active' %}
                      <span class="badge badge-success">Đang diễn ra</span>
                      {% elif schedule.status == 'upcoming' %}
                      <span class="badge badge-info">Sắp tới</span>
                      {% elif schedule.status == 'completed' %}
                      <span class="badge badge-secondary">Đã hoàn thành</span>
                      {% elif schedule.status == 'draft' %}
                      <span class="badge badge-warning">Bản nháp</span>
                      {% endif %}

                      {% if not schedule.is_published %}
                      <span class="badge badge-danger">Chưa công bố</span>
                      {% endif %}
                    </td>
                  </tr>
                </table>
              </div>
              <div class="col-md-6">
                <table class="table table-bordered">
                  <tr>
                    <th style="width: 200px">Mô tả</th>
                    <td>{{ schedule.description|default:"Không có mô tả"|linebreaks }}</td>
                  </tr>
                  <tr>
                    <th>Người tạo</th>
                    <td>{% if schedule.created_by %}{{ schedule.created_by.get_full_name|default:schedule.created_by.username }}{% else %}Không xác định{% endif %}</td>
                  </tr>
                  <tr>
                    <th>Ngày tạo</th>
                    <td>{{ schedule.created_at|date:"d/m/Y H:i" }}</td>
                  </tr>
                  <tr>
                    <th>Cập nhật lần cuối</th>
                    <td>{{ schedule.updated_at|date:"d/m/Y H:i" }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Phân công lịch trực -->
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-user-clock mr-1"></i>
              Phân công lịch trực
            </h3>
            <div class="card-tools">
              {% if can_edit %}
              <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#modal-assignment">
                <i class="fas fa-plus"></i> Thêm phân công
              </button>
              <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#modal-bulk-assignment">
                <i class="fas fa-users"></i> Phân công hàng loạt
              </button>
              {% endif %}
            </div>
          </div>
          <div class="card-body">
            {% if assignments %}
            <div class="table-responsive">
              <table class="table table-bordered schedule-table">
                <thead>
                  <tr>
                    <th style="width: 150px">Ngày</th>
                    {% for shift in shifts %}
                    <th>{{ shift.name }}<br><small>{{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}</small></th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% for date in date_range %}
                  <tr>
                    <td class="schedule-date">{{ date|date:"D, d/m/Y" }}</td>
                    {% for shift in shifts %}
                    <td>
                      {% with date_str=date|date:"Y-m-d" %}
                      {% with shift_id=shift.id %}
                      {% with date_shift=date_str|add:"_"|add:shift_id|stringformat:"s" %}
                      {% with assignments=schedule_data|get_assignment:date_shift %}
                      {% if assignments %}
                      {% for assignment in assignments %}
                      <div class="assignment-item">
                        <div class="staff-info">
                          <i class="fas fa-user-md mr-1"></i>
                          <strong>{{ assignment.user.get_full_name|default:assignment.user.username }}</strong>
                        </div>
                        {% if assignment.is_completed %}
                        <span class="status-badge status-completed">Hoàn thành</span>
                        {% elif assignment.is_confirmed %}
                        <span class="status-badge status-confirmed">Đã xác nhận</span>
                        {% else %}
                        <span class="status-badge status-pending">Chưa xác nhận</span>
                        {% endif %}
                        {% if assignment.notes %}
                        <div class="assignment-notes">
                          <small><i class="fas fa-sticky-note mr-1"></i> {{ assignment.notes }}</small>
                        </div>
                        {% endif %}
                        {% if can_edit %}
                        <div class="mt-1">
                          <button type="button" class="btn btn-xs btn-info btn-edit-assignment" data-id="{{ assignment.id }}" title="Chỉnh sửa">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button type="button" class="btn btn-xs btn-danger btn-delete-assignment" data-id="{{ assignment.id }}" title="Xóa">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                        {% endif %}
                      </div>
                      {% endfor %}
                      {% else %}
                      {% if can_edit %}
                      <button type="button" class="btn btn-xs btn-outline-primary btn-quick-assign" data-date="{{ date|date:'Y-m-d' }}" data-shift="{{ shift.id }}">
                        <i class="fas fa-plus"></i> Phân công
                      </button>
                      {% else %}
                      <span class="text-muted">Chưa có phân công</span>
                      {% endif %}
                      {% endif %}
                      {% endwith %}
                      {% endwith %}
                      {% endwith %}
                      {% endwith %}
                    </td>
                    {% endfor %}
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i> Chưa có phân công nào cho lịch trực này.
              {% if can_edit %}
              <button type="button" class="btn btn-sm btn-success ml-2" data-toggle="modal" data-target="#modal-assignment">
                <i class="fas fa-plus"></i> Thêm phân công
              </button>
              <button type="button" class="btn btn-sm btn-primary ml-2" data-toggle="modal" data-target="#modal-bulk-assignment">
                <i class="fas fa-users"></i> Phân công hàng loạt
              </button>
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modal thêm phân công -->
<div class="modal fade" id="modal-assignment">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Thêm phân công lịch trực</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form method="post" action="{% url 'schedules:assignment_create' %}">
        {% csrf_token %}
        <div class="modal-body">
          <div class="form-group">
            <label for="{{ assignment_form.schedule.id_for_label }}">Lịch trực</label>
            {{ assignment_form.schedule }}
          </div>
          <div class="form-group">
            <label for="{{ assignment_form.user.id_for_label }}">Nhân viên</label>
            {{ assignment_form.user }}
          </div>
          <div class="form-group">
            <label for="{{ assignment_form.shift.id_for_label }}">Ca trực</label>
            {{ assignment_form.shift }}
          </div>
          <div class="form-group">
            <label for="{{ assignment_form.date.id_for_label }}">Ngày trực</label>
            {{ assignment_form.date }}
            <small class="form-text text-muted">Ngày phải nằm trong khoảng thời gian của lịch trực ({{ schedule.start_date|date:"d/m/Y" }} - {{ schedule.end_date|date:"d/m/Y" }}).</small>
          </div>
          <div class="form-group">
            <label for="{{ assignment_form.notes.id_for_label }}">Ghi chú</label>
            {{ assignment_form.notes }}
          </div>
          <div class="form-group">
            <div class="custom-control custom-checkbox">
              {{ assignment_form.is_confirmed }}
              <label class="custom-control-label" for="{{ assignment_form.is_confirmed.id_for_label }}">Đã xác nhận</label>
            </div>
          </div>
        </div>
        <div class="modal-footer justify-content-between">
          <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
          <button type="submit" class="btn btn-primary">Lưu</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal phân công hàng loạt -->
<div class="modal fade" id="modal-bulk-assignment">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Phân công lịch trực hàng loạt</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form method="post" action="{% url 'schedules:bulk_assignment_create' %}">
        {% csrf_token %}
        <div class="modal-body">
          <div class="form-group">
            <label for="{{ bulk_form.schedule.id_for_label }}">Lịch trực</label>
            {{ bulk_form.schedule }}
          </div>
          <div class="form-group">
            <label for="{{ bulk_form.shift.id_for_label }}">Ca trực</label>
            {{ bulk_form.shift }}
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="{{ bulk_form.start_date.id_for_label }}">Ngày bắt đầu</label>
                {{ bulk_form.start_date }}
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="{{ bulk_form.end_date.id_for_label }}">Ngày kết thúc</label>
                {{ bulk_form.end_date }}
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="{{ bulk_form.users.id_for_label }}">Nhân viên</label>
            {{ bulk_form.users }}
            <small class="form-text text-muted">Có thể chọn nhiều nhân viên.</small>
          </div>
          <div class="form-group">
            <div class="custom-control custom-checkbox">
              {{ bulk_form.is_confirmed }}
              <label class="custom-control-label" for="{{ bulk_form.is_confirmed.id_for_label }}">Đã xác nhận</label>
            </div>
          </div>
        </div>
        <div class="modal-footer justify-content-between">
          <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
          <button type="submit" class="btn btn-primary">Lưu</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });

    // Xử lý xóa lịch trực
    $('.btn-delete-schedule').on('click', function() {
      const scheduleId = $(this).data('id');

      // Lấy thông tin lịch trực
      $.ajax({
        url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa lịch trực',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa lịch trực <strong>${data.title}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Thời gian: ${data.start_date} - ${data.end_date}</li>
                  <li>Số phân công: ${data.assignment_count}</li>
                  <li>Người tạo: ${data.created_by}</li>
                </ul>
                ${data.assignment_count > 0 ? '<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Lịch trực này đã có phân công. Xóa lịch trực sẽ xóa tất cả các phân công liên quan.</p>' : ''}
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Chuyển hướng về trang danh sách
                    window.location.href = "{% url 'schedules:schedule_list' %}";
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa lịch trực.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }

                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin lịch trực.',
            icon: 'error'
          });
        }
      });
    });

    // Xử lý xóa phân công
    $('.btn-delete-assignment').on('click', function() {
      const assignmentId = $(this).data('id');

      // Lấy thông tin phân công
      $.ajax({
        url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa phân công',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa phân công cho <strong>${data.user}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Lịch trực: ${data.schedule}</li>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Ca trực: ${data.shift}</li>
                  <li>Ngày: ${data.date}</li>
                  <li>Trạng thái: ${data.is_confirmed ? 'Đã xác nhận' : 'Chưa xác nhận'}</li>
                </ul>
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:assignment_delete' 0 %}".replace('0', assignmentId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Reload trang
                    window.location.reload();
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa phân công.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }

                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin phân công.',
            icon: 'error'
          });
        }
      });
    });

    // Xử lý phân công nhanh
    $('.btn-quick-assign').on('click', function() {
      const date = $(this).data('date');
      const shiftId = $(this).data('shift');

      // Điền thông tin vào form
      $('#{{ assignment_form.date.id_for_label }}').val(date);
      $('#{{ assignment_form.shift.id_for_label }}').val(shiftId).trigger('change');

      // Mở modal
      $('#modal-assignment').modal('show');
    });

    // Xử lý chỉnh sửa phân công
    $('.btn-edit-assignment').on('click', function() {
      const assignmentId = $(this).data('id');

      // Chuyển hướng đến trang chỉnh sửa
      window.location.href = "{% url 'schedules:assignment_edit' 0 %}".replace('0', assignmentId);
    });
  });
</script>
{% endblock %}
