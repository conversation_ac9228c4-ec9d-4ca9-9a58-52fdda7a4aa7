from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse
from datetime import timedelta, datetime
import json
from .models import Holiday
from .models import DutyShift, DutySchedule, DutyAssignment, DutyNotification
from departments.models import Department
from django.contrib.auth.models import User
from .forms import DutyShiftForm, DutyScheduleForm, DutyAssignmentForm, BulkAssignmentForm
from .decorators import (
    can_view_shifts, can_create_shifts, can_manage_shifts, can_delete_shifts,
    can_view_schedules, can_create_schedules, can_manage_schedules, can_delete_schedules,
    can_view_assignments, can_create_assignments, can_manage_assignments, can_delete_assignments
)

@login_required
def dashboard(request):
    """Hiển thị trang dashboard của phân hệ lịch trực"""
    # L<PERSON>y lịch trực đang diễn ra
    today = timezone.now().date()
    active_schedules = DutySchedule.objects.filter(
        start_date__lte=today,
        end_date__gte=today,
        is_published=True
    ).order_by('department__name')[:5]

    # Lấy lịch trực sắp tới
    upcoming_schedules = DutySchedule.objects.filter(
        start_date__gt=today,
        is_published=True
    ).order_by('start_date')[:5]

    # Lấy ca trực của người dùng hiện tại
    user_assignments = DutyAssignment.objects.filter(
        user=request.user,
        date__gte=today
    ).order_by('date', 'shift__start_time')[:10]

    # Lấy thông báo chưa đọc
    unread_notifications = DutyNotification.objects.filter(
        assignment__user=request.user,
        is_read=False
    ).order_by('-sent_at')

    context = {
        'active_schedules': active_schedules,
        'upcoming_schedules': upcoming_schedules,
        'user_assignments': user_assignments,
        'unread_notifications': unread_notifications,
        'unread_count': unread_notifications.count(),
    }

    return render(request, 'schedules/dashboard.html', context)

# Views cho Ca trực
@login_required
@can_view_shifts
def shift_list(request):
    """Hiển thị danh sách ca trực"""
    shifts = DutyShift.objects.all().order_by('start_time')

    # Tìm kiếm
    search_query = request.GET.get('search')
    if search_query:
        shifts = shifts.filter(name__icontains=search_query)

    # Phân trang
    paginator = Paginator(shifts, 10)
    page = request.GET.get('page')
    shifts = paginator.get_page(page)

    # Thống kê số lượng phân công cho mỗi ca trực
    shift_stats = {}
    for shift in shifts:
        assignment_count = DutyAssignment.objects.filter(shift=shift).count()
        shift_stats[shift.id] = assignment_count

    context = {
        'shifts': shifts,
        'search_query': search_query,
        'shift_stats': shift_stats,
    }

    return render(request, 'schedules/shift_list.html', context)

@login_required
@can_create_shifts
def shift_create(request):
    """Tạo ca trực mới"""
    if request.method == 'POST':
        form = DutyShiftForm(request.POST)
        if form.is_valid():
            shift = form.save()
            messages.success(request, f'Ca trực "{shift.name}" đã được tạo thành công!')
            return redirect('schedules:shift_list')
    else:
        form = DutyShiftForm(initial={'color': '#3c8dbc'})

    context = {
        'form': form,
        'is_edit': False,
    }

    return render(request, 'schedules/shift_form.html', context)

@login_required
@can_manage_shifts
def shift_edit(request, pk):
    """Chỉnh sửa ca trực"""
    shift = get_object_or_404(DutyShift, pk=pk)

    if request.method == 'POST':
        form = DutyShiftForm(request.POST, instance=shift)
        if form.is_valid():
            shift = form.save()
            messages.success(request, f'Ca trực "{shift.name}" đã được cập nhật thành công!')
            return redirect('schedules:shift_list')
    else:
        form = DutyShiftForm(instance=shift)

    # Kiểm tra xem ca trực này đã được sử dụng trong phân công chưa
    assignment_count = DutyAssignment.objects.filter(shift=shift).count()

    context = {
        'form': form,
        'shift': shift,
        'is_edit': True,
        'assignment_count': assignment_count,
    }

    return render(request, 'schedules/shift_form.html', context)

@login_required
@can_delete_shifts
def shift_delete(request, pk):
    """Xóa ca trực"""
    shift = get_object_or_404(DutyShift, pk=pk)

    # Kiểm tra xem ca trực này đã được sử dụng trong phân công chưa
    assignment_count = DutyAssignment.objects.filter(shift=shift).count()

    if assignment_count > 0:
        if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': f'Không thể xóa ca trực "{shift.name}" vì đã được sử dụng trong {assignment_count} phân công!'
            }, status=400)
        else:
            messages.error(request, f'Không thể xóa ca trực "{shift.name}" vì đã được sử dụng trong {assignment_count} phân công!')
            return redirect('schedules:shift_list')

    if request.method == 'POST':
        name = shift.name
        shift.delete()

        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Ca trực "{name}" đã được xóa thành công!'
            })
        else:
            messages.success(request, f'Ca trực "{name}" đã được xóa thành công!')
            return redirect('schedules:shift_list')

    # Nếu là request AJAX GET, trả về thông tin ca trực để hiển thị trong SweetAlert
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'id': shift.id,
            'name': shift.name,
            'start_time': shift.start_time.strftime('%H:%M'),
            'end_time': shift.end_time.strftime('%H:%M'),
            'duration': shift.duration,
            'color': shift.color,
            'description': shift.description or 'Không có mô tả',
            'assignment_count': assignment_count
        })

    context = {
        'shift': shift,
        'assignment_count': assignment_count,
    }

    return render(request, 'schedules/shift_confirm_delete.html', context)

# Views cho Lịch trực
@login_required
@can_view_schedules
def schedule_list(request):
    """Hiển thị danh sách lịch trực"""
    # Nếu là superuser hoặc admin, hiển thị tất cả lịch trực
    if request.user.is_superuser or request.user.is_staff:
        schedules = DutySchedule.objects.all()
    else:
        # Nếu là người dùng thường, chỉ hiển thị lịch trực của khoa/phòng ban của họ
        # hoặc lịch trực mà họ được phân công
        user_department = request.user.profile.department
        if user_department:
            # Lấy lịch trực của khoa/phòng ban
            department_schedules = DutySchedule.objects.filter(department=user_department)
            # Lấy lịch trực mà người dùng được phân công
            assigned_schedules = DutySchedule.objects.filter(
                assignments__user=request.user
            ).distinct()
            # Kết hợp hai danh sách
            schedules = (department_schedules | assigned_schedules).distinct()
        else:
            # Nếu người dùng không thuộc khoa/phòng ban nào, chỉ hiển thị lịch trực mà họ được phân công
            schedules = DutySchedule.objects.filter(
                assignments__user=request.user
            ).distinct()

    # Sắp xếp theo ngày bắt đầu giảm dần
    schedules = schedules.order_by('-start_date')

    # Tìm kiếm
    search_query = request.GET.get('search')
    if search_query:
        schedules = schedules.filter(
            Q(title__icontains=search_query) |
            Q(department__name__icontains=search_query)
        )

    # Lọc theo trạng thái
    status_filter = request.GET.get('status')
    if status_filter:
        today = timezone.now().date()
        if status_filter == 'active':
            schedules = schedules.filter(
                start_date__lte=today,
                end_date__gte=today,
                is_published=True
            )
        elif status_filter == 'upcoming':
            schedules = schedules.filter(
                start_date__gt=today,
                is_published=True
            )
        elif status_filter == 'completed':
            schedules = schedules.filter(
                end_date__lt=today,
                is_published=True
            )
        elif status_filter == 'draft':
            # Chỉ superuser và admin mới có thể xem bản nháp
            if request.user.is_superuser or request.user.is_staff:
                schedules = schedules.filter(is_published=False)
            else:
                schedules = schedules.filter(is_published=True)  # Đảm bảo người dùng thường không thấy bản nháp

    # Lọc theo khoa/phòng ban
    department_filter = request.GET.get('department')
    if department_filter:
        schedules = schedules.filter(department_id=department_filter)

    # Phân trang
    paginator = Paginator(schedules, 10)
    page = request.GET.get('page')
    schedules = paginator.get_page(page)

    # Lấy danh sách khoa/phòng ban cho bộ lọc
    if request.user.is_superuser or request.user.is_staff:
        departments = Department.objects.all().order_by('name')
    else:
        # Người dùng thường chỉ thấy khoa/phòng ban của họ
        departments = Department.objects.filter(id=request.user.profile.department_id) if request.user.profile.department else Department.objects.none()

    # Thống kê số lượng phân công cho mỗi lịch trực
    schedule_stats = {}
    for schedule in schedules:
        assignment_count = DutyAssignment.objects.filter(schedule=schedule).count()
        schedule_stats[schedule.id] = assignment_count

    context = {
        'schedules': schedules,
        'search_query': search_query,
        'status_filter': status_filter,
        'department_filter': department_filter,
        'departments': departments,
        'schedule_stats': schedule_stats,
    }

    return render(request, 'schedules/schedule_list.html', context)

@login_required
@can_create_schedules
def schedule_create(request):
    """Tạo lịch trực mới"""
    if request.method == 'POST':
        form = DutyScheduleForm(request.POST)
        if form.is_valid():
            schedule = form.save(commit=False)
            schedule.created_by = request.user
            schedule.save()

            messages.success(request, f'Lịch trực "{schedule.title}" đã được tạo thành công!')
            return redirect('schedules:schedule_detail', pk=schedule.pk)
    else:
        # Nếu người dùng không phải là superuser và có khoa/phòng ban, đặt khoa/phòng ban mặc định
        initial = {}
        if not request.user.is_superuser and request.user.profile.department:
            initial['department'] = request.user.profile.department

        form = DutyScheduleForm(initial=initial)

        # Nếu người dùng không phải là superuser, chỉ cho phép chọn khoa/phòng ban của họ
        if not request.user.is_superuser and request.user.profile.department:
            form.fields['department'].queryset = Department.objects.filter(id=request.user.profile.department.id)

    context = {
        'form': form,
        'is_edit': False,
    }

    return render(request, 'schedules/schedule_form.html', context)

@login_required
@can_view_schedules
def schedule_detail(request, pk):
    """Xem chi tiết lịch trực"""
    schedule = get_object_or_404(DutySchedule, pk=pk)

    # Kiểm tra quyền truy cập
    if not (request.user.is_superuser or request.user.is_staff):
        # Nếu không phải là superuser hoặc admin, kiểm tra xem người dùng có thuộc khoa/phòng ban của lịch trực không
        # hoặc có được phân công trong lịch trực này không
        user_department = None
        if hasattr(request.user, 'profile') and request.user.profile:
            user_department = request.user.profile.department

        is_assigned = DutyAssignment.objects.filter(schedule=schedule, user=request.user).exists()

        if not (user_department and user_department == schedule.department) and not is_assigned:
            messages.error(request, 'Bạn không có quyền xem lịch trực này.')
            return redirect('schedules:schedule_list')

        # Nếu lịch trực chưa được công bố và người dùng không phải là người tạo
        if not schedule.is_published and schedule.created_by and schedule.created_by != request.user:
            messages.error(request, 'Lịch trực này chưa được công bố.')
            return redirect('schedules:schedule_list')

    # Lấy danh sách phân công theo lịch trực
    assignments = DutyAssignment.objects.filter(schedule=schedule).order_by('date', 'shift__start_time')

    # Lấy danh sách ca trực
    shifts = DutyShift.objects.all().order_by('start_time')

    # Lấy danh sách nhân viên thuộc khoa/phòng ban
    users = User.objects.filter(profile__department=schedule.department, is_active=True).order_by('last_name', 'first_name')

    # Tạo form phân công mới
    assignment_form = DutyAssignmentForm(initial={'schedule': schedule})
    assignment_form.fields['schedule'].queryset = DutySchedule.objects.filter(pk=schedule.pk)
    assignment_form.fields['user'].queryset = users

    # Tạo form phân công hàng loạt
    bulk_form = BulkAssignmentForm(initial={'schedule': schedule})
    bulk_form.fields['schedule'].queryset = DutySchedule.objects.filter(pk=schedule.pk)
    bulk_form.fields['users'].queryset = users

    # Tạo cấu trúc dữ liệu cho lịch trực
    schedule_data = {}
    for assignment in assignments:
        date_str = assignment.date.strftime('%Y-%m-%d')
        if date_str not in schedule_data:
            schedule_data[date_str] = {}

        shift_id = assignment.shift.id
        if shift_id not in schedule_data[date_str]:
            schedule_data[date_str][shift_id] = []

        schedule_data[date_str][shift_id].append(assignment)

    # Tạo danh sách các ngày trong khoảng thời gian lịch trực
    date_range = []
    current_date = schedule.start_date
    while current_date <= schedule.end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    context = {
        'schedule': schedule,
        'assignments': assignments,
        'shifts': shifts,
        'users': users,
        'assignment_form': assignment_form,
        'bulk_form': bulk_form,
        'schedule_data': schedule_data,
        'date_range': date_range,
        'can_edit': request.user.is_superuser or request.user.is_staff or (schedule.created_by and request.user == schedule.created_by),
        'can_delete': request.user.is_superuser or (schedule.created_by and request.user == schedule.created_by),
    }

    return render(request, 'schedules/schedule_detail.html', context)

@login_required
@can_manage_schedules
def schedule_edit(request, pk):
    """Chỉnh sửa lịch trực"""
    schedule = get_object_or_404(DutySchedule, pk=pk)

    # Kiểm tra quyền chỉnh sửa
    if not (request.user.is_superuser or request.user.is_staff or request.user == schedule.created_by):
        messages.error(request, 'Bạn không có quyền chỉnh sửa lịch trực này.')
        return redirect('schedules:schedule_detail', pk=schedule.pk)

    if request.method == 'POST':
        form = DutyScheduleForm(request.POST, instance=schedule)
        if form.is_valid():
            form.save()
            messages.success(request, f'Lịch trực "{schedule.title}" đã được cập nhật thành công!')
            return redirect('schedules:schedule_detail', pk=schedule.pk)
    else:
        form = DutyScheduleForm(instance=schedule)

        # Nếu người dùng không phải là superuser, chỉ cho phép chọn khoa/phòng ban của họ
        if not request.user.is_superuser and request.user.profile.department:
            form.fields['department'].queryset = Department.objects.filter(id=request.user.profile.department.id)

    # Kiểm tra xem lịch trực này đã có phân công chưa
    assignment_count = DutyAssignment.objects.filter(schedule=schedule).count()

    context = {
        'form': form,
        'schedule': schedule,
        'is_edit': True,
        'assignment_count': assignment_count,
    }

    return render(request, 'schedules/schedule_form.html', context)

@login_required
@can_delete_schedules
def schedule_delete(request, pk):
    """Xóa lịch trực"""
    schedule = get_object_or_404(DutySchedule, pk=pk)

    # Kiểm tra quyền xóa
    if not (request.user.is_superuser or request.user == schedule.created_by):
        if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Bạn không có quyền xóa lịch trực này.'
            }, status=403)
        else:
            messages.error(request, 'Bạn không có quyền xóa lịch trực này.')
            return redirect('schedules:schedule_detail', pk=schedule.pk)

    # Kiểm tra xem lịch trực này đã có phân công chưa
    assignment_count = DutyAssignment.objects.filter(schedule=schedule).count()

    if assignment_count > 0 and not request.user.is_superuser:
        if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': f'Không thể xóa lịch trực "{schedule.title}" vì đã có {assignment_count} phân công!'
            }, status=400)
        else:
            messages.error(request, f'Không thể xóa lịch trực "{schedule.title}" vì đã có {assignment_count} phân công!')
            return redirect('schedules:schedule_detail', pk=schedule.pk)

    if request.method == 'POST':
        title = schedule.title
        schedule.delete()

        if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Lịch trực "{title}" đã được xóa thành công!'
            })
        else:
            messages.success(request, f'Lịch trực "{title}" đã được xóa thành công!')
            return redirect('schedules:schedule_list')

    # Nếu là request AJAX GET, trả về thông tin lịch trực để hiển thị trong SweetAlert
    if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
        created_by = "Không xác định"
        if schedule.created_by:
            created_by = schedule.created_by.get_full_name() or schedule.created_by.username

        return JsonResponse({
            'id': schedule.id,
            'title': schedule.title,
            'department': schedule.department.name,
            'start_date': schedule.start_date.strftime('%d/%m/%Y'),
            'end_date': schedule.end_date.strftime('%d/%m/%Y'),
            'is_published': schedule.is_published,
            'description': schedule.description or 'Không có mô tả',
            'assignment_count': assignment_count,
            'created_by': created_by,
            'can_delete': request.user.is_superuser or (assignment_count == 0)
        })

    context = {
        'schedule': schedule,
        'assignment_count': assignment_count,
    }

    return render(request, 'schedules/schedule_confirm_delete.html', context)

# Views cho Phân công lịch trực
@login_required
@can_view_assignments
def assignment_list(request):
    """Hiển thị danh sách phân công lịch trực"""
    # Nếu là superuser hoặc admin, hiển thị tất cả phân công
    if request.user.is_superuser or request.user.is_staff:
        assignments = DutyAssignment.objects.all()
    else:
        # Nếu là người dùng thường, chỉ hiển thị phân công của họ hoặc trong khoa/phòng ban của họ
        user_department = request.user.profile.department
        if user_department:
            # Lấy phân công của khoa/phòng ban
            assignments = DutyAssignment.objects.filter(
                Q(user=request.user) | Q(schedule__department=user_department)
            ).distinct()
        else:
            # Nếu người dùng không thuộc khoa/phòng ban nào, chỉ hiển thị phân công của họ
            assignments = DutyAssignment.objects.filter(user=request.user)

    # Sắp xếp theo ngày giảm dần và ca trực
    assignments = assignments.order_by('-date', 'shift__start_time')

    # Tìm kiếm
    search_query = request.GET.get('search')
    if search_query:
        assignments = assignments.filter(
            Q(user__username__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(schedule__title__icontains=search_query)
        )

    # Lọc theo lịch trực
    schedule_filter = request.GET.get('schedule')
    if schedule_filter:
        assignments = assignments.filter(schedule_id=schedule_filter)

    # Lọc theo ngày
    date_filter = request.GET.get('date')
    if date_filter:
        assignments = assignments.filter(date=date_filter)

    # Lọc theo trạng thái
    status_filter = request.GET.get('status')
    if status_filter:
        if status_filter == 'confirmed':
            assignments = assignments.filter(is_confirmed=True, is_completed=False)
        elif status_filter == 'completed':
            assignments = assignments.filter(is_completed=True)
        elif status_filter == 'pending':
            assignments = assignments.filter(is_confirmed=False, is_completed=False)

    # Lọc theo người dùng
    user_filter = request.GET.get('user')
    if user_filter:
        assignments = assignments.filter(user_id=user_filter)

    # Phân trang
    paginator = Paginator(assignments, 20)
    page = request.GET.get('page')
    assignments = paginator.get_page(page)

    # Lấy danh sách lịch trực cho bộ lọc
    if request.user.is_superuser or request.user.is_staff:
        schedules = DutySchedule.objects.all().order_by('-start_date')
    else:
        # Người dùng thường chỉ thấy lịch trực của khoa/phòng ban của họ
        user_department = request.user.profile.department
        if user_department:
            schedules = DutySchedule.objects.filter(department=user_department).order_by('-start_date')
        else:
            schedules = DutySchedule.objects.filter(
                assignments__user=request.user
            ).distinct().order_by('-start_date')

    # Lấy danh sách người dùng cho bộ lọc
    if request.user.is_superuser or request.user.is_staff:
        users = User.objects.filter(is_active=True).order_by('last_name', 'first_name')
    else:
        # Người dùng thường chỉ thấy người dùng trong khoa/phòng ban của họ
        user_department = request.user.profile.department
        if user_department:
            users = User.objects.filter(profile__department=user_department, is_active=True).order_by('last_name', 'first_name')
        else:
            users = User.objects.filter(id=request.user.id)

    context = {
        'assignments': assignments,
        'search_query': search_query,
        'schedule_filter': schedule_filter,
        'date_filter': date_filter,
        'status_filter': status_filter,
        'user_filter': user_filter,
        'schedules': schedules,
        'users': users,
    }

    return render(request, 'schedules/assignment_list.html', context)

@login_required
@can_create_assignments
def assignment_create(request):
    """Tạo phân công lịch trực mới"""
    if request.method == 'POST':
        form = DutyAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save()
            messages.success(request, f'Phân công lịch trực cho {assignment.user.get_full_name()} đã được tạo thành công!')
            return redirect('schedules:schedule_detail', pk=assignment.schedule.pk)
    else:
        # Nếu có schedule_id trong query string, đặt giá trị mặc định
        schedule_id = request.GET.get('schedule')
        initial = {}
        if schedule_id:
            try:
                schedule = DutySchedule.objects.get(pk=schedule_id)
                initial['schedule'] = schedule

                # Nếu có date trong query string, đặt giá trị mặc định
                date = request.GET.get('date')
                if date:
                    initial['date'] = date
            except DutySchedule.DoesNotExist:
                pass

        form = DutyAssignmentForm(initial=initial)

        # Nếu người dùng không phải là superuser, giới hạn các lựa chọn
        if not request.user.is_superuser:
            # Nếu người dùng có khoa/phòng ban, chỉ hiển thị lịch trực của khoa/phòng ban đó
            user_department = request.user.profile.department
            if user_department:
                form.fields['schedule'].queryset = DutySchedule.objects.filter(
                    department=user_department
                ).order_by('-start_date')

                # Chỉ hiển thị người dùng trong khoa/phòng ban
                form.fields['user'].queryset = User.objects.filter(
                    profile__department=user_department,
                    is_active=True
                ).order_by('last_name', 'first_name')

    context = {
        'form': form,
        'is_edit': False,
    }

    return render(request, 'schedules/assignment_form.html', context)

@login_required
@can_manage_assignments
def assignment_edit(request, pk):
    """Chỉnh sửa phân công lịch trực"""
    assignment = get_object_or_404(DutyAssignment, pk=pk)

    # Kiểm tra quyền chỉnh sửa
    if not (request.user.is_superuser or request.user.is_staff):
        # Nếu không phải là superuser hoặc admin, kiểm tra xem người dùng có phải là người được phân công
        # hoặc người tạo lịch trực không
        if request.user != assignment.user and request.user != assignment.schedule.created_by:
            messages.error(request, 'Bạn không có quyền chỉnh sửa phân công lịch trực này.')
            return redirect('schedules:assignment_list')

    if request.method == 'POST':
        form = DutyAssignmentForm(request.POST, instance=assignment)
        if form.is_valid():
            assignment = form.save()
            messages.success(request, f'Phân công lịch trực cho {assignment.user.get_full_name()} đã được cập nhật thành công!')
            return redirect('schedules:schedule_detail', pk=assignment.schedule.pk)
    else:
        form = DutyAssignmentForm(instance=assignment)

        # Nếu người dùng không phải là superuser, giới hạn các lựa chọn
        if not request.user.is_superuser:
            # Nếu người dùng có khoa/phòng ban, chỉ hiển thị lịch trực của khoa/phòng ban đó
            user_department = request.user.profile.department
            if user_department:
                form.fields['schedule'].queryset = DutySchedule.objects.filter(
                    department=user_department
                ).order_by('-start_date')

                # Chỉ hiển thị người dùng trong khoa/phòng ban
                form.fields['user'].queryset = User.objects.filter(
                    profile__department=user_department,
                    is_active=True
                ).order_by('last_name', 'first_name')

    context = {
        'form': form,
        'assignment': assignment,
        'is_edit': True,
    }

    return render(request, 'schedules/assignment_form.html', context)

@login_required
@can_delete_assignments
def assignment_delete(request, pk):
    """Xóa phân công lịch trực"""
    assignment = get_object_or_404(DutyAssignment, pk=pk)

    # Kiểm tra quyền xóa
    if not (request.user.is_superuser or request.user.is_staff):
        # Nếu không phải là superuser hoặc admin, kiểm tra xem người dùng có phải là người tạo lịch trực không
        if request.user != assignment.schedule.created_by:
            if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': 'Bạn không có quyền xóa phân công lịch trực này.'
                }, status=403)
            else:
                messages.error(request, 'Bạn không có quyền xóa phân công lịch trực này.')
                return redirect('schedules:assignment_list')

    if request.method == 'POST':
        schedule_id = assignment.schedule.id
        user_name = assignment.user.get_full_name()
        assignment.delete()

        if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'Phân công lịch trực cho {user_name} đã được xóa thành công!'
            })
        else:
            messages.success(request, f'Phân công lịch trực cho {user_name} đã được xóa thành công!')
            return redirect('schedules:schedule_detail', pk=schedule_id)

    # Nếu là request AJAX GET, trả về thông tin phân công để hiển thị trong SweetAlert
    if request.is_ajax() or request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'id': assignment.id,
            'user': assignment.user.get_full_name() or assignment.user.username,
            'schedule': assignment.schedule.title,
            'department': assignment.schedule.department.name,
            'shift': f"{assignment.shift.name} ({assignment.shift.start_time.strftime('%H:%M')} - {assignment.shift.end_time.strftime('%H:%M')})",
            'date': assignment.date.strftime('%d/%m/%Y'),
            'is_confirmed': assignment.is_confirmed,
            'is_completed': assignment.is_completed,
            'notes': assignment.notes or 'Không có ghi chú'
        })

    context = {
        'assignment': assignment,
    }

    return render(request, 'schedules/assignment_confirm_delete.html', context)

@login_required
@can_create_assignments
def bulk_assignment_create(request):
    """Tạo phân công lịch trực hàng loạt"""
    if request.method == 'POST':
        form = BulkAssignmentForm(request.POST)
        if form.is_valid():
            schedule = form.cleaned_data['schedule']
            shift = form.cleaned_data['shift']
            start_date = form.cleaned_data['start_date']
            end_date = form.cleaned_data['end_date']
            users = form.cleaned_data['users']
            is_confirmed = form.cleaned_data['is_confirmed']

            # Tạo danh sách các ngày trong khoảng thời gian
            date_range = []
            current_date = start_date
            while current_date <= end_date:
                date_range.append(current_date)
                current_date += timedelta(days=1)

            # Đếm số phân công đã tạo
            created_count = 0
            skipped_count = 0

            # Tạo phân công cho mỗi người dùng và mỗi ngày
            for user in users:
                for date in date_range:
                    # Kiểm tra xem đã có phân công cho người dùng này vào ngày và ca trực này chưa
                    existing = DutyAssignment.objects.filter(
                        schedule=schedule,
                        user=user,
                        shift=shift,
                        date=date
                    ).exists()

                    if not existing:
                        try:
                            DutyAssignment.objects.create(
                                schedule=schedule,
                                user=user,
                                shift=shift,
                                date=date,
                                is_confirmed=is_confirmed
                            )
                            created_count += 1
                        except Exception:
                            skipped_count += 1
                    else:
                        skipped_count += 1

            if created_count > 0:
                messages.success(request, f'Đã tạo thành công {created_count} phân công lịch trực.')

            if skipped_count > 0:
                messages.warning(request, f'Đã bỏ qua {skipped_count} phân công do trùng lặp hoặc lỗi.')

            return redirect('schedules:schedule_detail', pk=schedule.pk)
    else:
        # Nếu có schedule_id trong query string, đặt giá trị mặc định
        schedule_id = request.GET.get('schedule')
        initial = {}
        if schedule_id:
            try:
                schedule = DutySchedule.objects.get(pk=schedule_id)
                initial['schedule'] = schedule
                initial['start_date'] = schedule.start_date
                initial['end_date'] = schedule.end_date
            except DutySchedule.DoesNotExist:
                pass

        form = BulkAssignmentForm(initial=initial)

        # Nếu người dùng không phải là superuser, giới hạn các lựa chọn
        if not request.user.is_superuser:
            # Nếu người dùng có khoa/phòng ban, chỉ hiển thị lịch trực của khoa/phòng ban đó
            user_department = request.user.profile.department
            if user_department:
                form.fields['schedule'].queryset = DutySchedule.objects.filter(
                    department=user_department
                ).order_by('-start_date')

                # Chỉ hiển thị người dùng trong khoa/phòng ban
                form.fields['users'].queryset = User.objects.filter(
                    profile__department=user_department,
                    is_active=True
                ).order_by('last_name', 'first_name')

    context = {
        'form': form,
    }

    return render(request, 'schedules/bulk_assignment_form.html', context)

# Views cho lịch trực theo khoa/phòng ban
@login_required
def schedule_by_department(request, department_id):
    """Xem lịch trực theo khoa/phòng ban"""
    department = get_object_or_404(Department, pk=department_id)

    # Kiểm tra quyền truy cập
    if not (request.user.is_superuser or request.user.is_staff):
        # Nếu không phải là superuser hoặc admin, kiểm tra xem người dùng có thuộc khoa/phòng ban này không
        user_department = request.user.profile.department
        if not user_department or user_department.id != department.id:
            messages.error(request, 'Bạn không có quyền xem lịch trực của khoa/phòng ban này.')
            return redirect('schedules:schedule_list')

    # Lấy lịch trực của khoa/phòng ban
    schedules = DutySchedule.objects.filter(
        department=department,
        is_published=True
    ).order_by('-start_date')

    # Lọc theo trạng thái
    status_filter = request.GET.get('status')
    if status_filter:
        today = timezone.now().date()
        if status_filter == 'active':
            schedules = schedules.filter(
                start_date__lte=today,
                end_date__gte=today
            )
        elif status_filter == 'upcoming':
            schedules = schedules.filter(start_date__gt=today)
        elif status_filter == 'completed':
            schedules = schedules.filter(end_date__lt=today)

    # Phân trang
    paginator = Paginator(schedules, 10)
    page = request.GET.get('page')
    schedules = paginator.get_page(page)

    # Thống kê số lượng phân công cho mỗi lịch trực
    schedule_stats = {}
    for schedule in schedules:
        assignment_count = DutyAssignment.objects.filter(schedule=schedule).count()
        schedule_stats[schedule.id] = assignment_count

    context = {
        'department': department,
        'schedules': schedules,
        'status_filter': status_filter,
        'schedule_stats': schedule_stats,
    }

    return render(request, 'schedules/schedule_by_department.html', context)

# Views cho lịch trực cá nhân
@login_required
def my_schedule(request):
    """Xem lịch trực cá nhân"""
    today = timezone.now().date()

    # Kiểm tra xem có chọn khoa/phòng ban không
    department_id = request.GET.get('department')
    selected_department = None

    # Lấy danh sách ca trực
    shifts = DutyShift.objects.all().order_by('start_time')

    # Xác định khoảng thời gian hiển thị (mặc định là tuần hiện tại từ thứ 2 đến chủ nhật)
    start_date = today - timedelta(days=today.weekday())  # Thứ 2 của tuần hiện tại (weekday=0 là thứ 2)
    end_date = start_date + timedelta(days=6)  # Chủ nhật của tuần hiện tại
    date_range_str = None

    # Kiểm tra xem có chọn khoảng thời gian không
    date_range_param = request.GET.get('date_range')
    if date_range_param:
        try:
            start_date_str, end_date_str = date_range_param.split(' - ')
            start_date = datetime.strptime(start_date_str, '%d/%m/%Y').date()
            end_date = datetime.strptime(end_date_str, '%d/%m/%Y').date()
            date_range_str = date_range_param
        except (ValueError, TypeError):
            # Nếu có lỗi, sử dụng khoảng thời gian mặc định
            pass

    # Lọc theo trạng thái
    status_filter = request.GET.get('status')
    if status_filter and not date_range_param:
        if status_filter == 'upcoming':
            start_date = today + timedelta(days=1)
            end_date = today + timedelta(days=14)  # Hiển thị 2 tuần tới
            date_range_str = f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"
        elif status_filter == 'today':
            start_date = today
            end_date = today
            date_range_str = f"{today.strftime('%d/%m/%Y')} - {today.strftime('%d/%m/%Y')}"
        elif status_filter == 'completed':
            start_date = today - timedelta(days=7)  # Hiển thị 1 tuần trước
            end_date = today - timedelta(days=1)
            date_range_str = f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"

    # Nếu không có date_range_str, tạo từ start_date và end_date
    if not date_range_str:
        date_range_str = f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"

    # Tạo danh sách các ngày trong khoảng thời gian
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    # Cấu trúc dữ liệu cho lịch trực
    schedule_data = {}

    # Lấy danh sách khoa/phòng ban mà người dùng có thể xem
    departments = []
    if request.user.is_superuser or request.user.is_staff:
        departments = Department.objects.all().order_by('name')
    elif hasattr(request.user, 'profile') and request.user.profile.department:
        departments = [request.user.profile.department]

    if department_id:
        # Xem lịch trực của khoa/phòng ban
        try:
            selected_department = Department.objects.get(pk=department_id)

            # Kiểm tra quyền xem lịch trực của khoa/phòng ban
            if not (request.user.is_superuser or request.user.is_staff) and (not hasattr(request.user, 'profile') or request.user.profile.department != selected_department):
                # Nếu không có quyền, chuyển về xem lịch trực cá nhân
                selected_department = None
                department_id = None
                messages.warning(request, "Bạn không có quyền xem lịch trực của khoa/phòng ban này.")
            else:
                # Lấy tất cả lịch trực của khoa/phòng ban trong khoảng thời gian
                schedules = DutySchedule.objects.filter(
                    department=selected_department,
                    start_date__lte=end_date,
                    end_date__gte=start_date
                )

                # Lấy tất cả phân công trong các lịch trực
                assignments = DutyAssignment.objects.filter(
                    schedule__in=schedules,
                    date__gte=start_date,
                    date__lte=end_date
                ).select_related('user', 'shift', 'schedule').order_by('date', 'shift__start_time')

        except Department.DoesNotExist:
            assignments = []
            messages.error(request, "Không tìm thấy khoa/phòng ban.")
    else:
        # Xem lịch trực cá nhân
        assignments = DutyAssignment.objects.filter(
            user=request.user,
            date__gte=start_date,
            date__lte=end_date
        ).select_related('user', 'shift', 'schedule').order_by('date', 'shift__start_time')

    # Tạo cấu trúc dữ liệu cho lịch trực
    for assignment in assignments:
        date_str = assignment.date.strftime('%Y-%m-%d')
        if date_str not in schedule_data:
            schedule_data[date_str] = {}

        shift_id = str(assignment.shift.id)
        if shift_id not in schedule_data[date_str]:
            schedule_data[date_str][shift_id] = []

        schedule_data[date_str][shift_id].append(assignment)

    # Đánh dấu thông báo đã đọc
    if 'mark_read' in request.GET:
        notifications_count = DutyNotification.objects.filter(
            assignment__user=request.user,
            is_read=False
        ).update(is_read=True, read_at=timezone.now())
        messages.success(request, f'Đã đánh dấu {notifications_count} thông báo là đã đọc!')

    # Lấy thông báo chưa đọc
    unread_notifications = DutyNotification.objects.filter(
        assignment__user=request.user,
        is_read=False
    ).order_by('-sent_at')

    # Thống kê
    upcoming_count = DutyAssignment.objects.filter(
        user=request.user,
        date__gt=today
    ).count()

    today_count = DutyAssignment.objects.filter(
        user=request.user,
        date=today
    ).count()

    completed_count = DutyAssignment.objects.filter(
        user=request.user,
        date__lt=today
    ).count()

    context = {
        'assignments': assignments,
        'status_filter': status_filter,
        'today': today,
        'unread_notifications': unread_notifications,
        'unread_count': unread_notifications.count(),
        'upcoming_count': upcoming_count,
        'today_count': today_count,
        'completed_count': completed_count,
        'shifts': shifts,
        'date_range': date_range,
        'date_range_str': date_range_str,
        'schedule_data': schedule_data,
        'selected_department': selected_department.id if selected_department else None,
        'departments': departments,
    }

    return render(request, 'schedules/my_schedule.html', context)

# API views
@login_required
def get_users_by_department(request):
    """API để lấy danh sách người dùng theo khoa/phòng ban"""
    department_id = request.GET.get('department_id')
    if not department_id:
        return JsonResponse({'error': 'Thiếu department_id'}, status=400)

    try:
        department = Department.objects.get(pk=department_id)
        users = User.objects.filter(
            profile__department=department,
            is_active=True
        ).order_by('last_name', 'first_name')

        user_list = [
            {
                'id': user.id,
                'name': user.get_full_name() or user.username,
                'username': user.username
            }
            for user in users
        ]

        return JsonResponse({'users': user_list})
    except Department.DoesNotExist:
        return JsonResponse({'error': 'Khoa/phòng ban không tồn tại'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def toggle_assignment_status(request, pk):
    """API để chuyển đổi trạng thái phân công"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)

    try:
        assignment = DutyAssignment.objects.get(pk=pk)

        # Kiểm tra quyền
        if not (request.user.is_superuser or request.user.is_staff or request.user == assignment.user or request.user == assignment.schedule.created_by):
            return JsonResponse({'error': 'Bạn không có quyền thực hiện hành động này'}, status=403)

        status_type = request.POST.get('status_type')
        if status_type == 'confirmed':
            assignment.is_confirmed = not assignment.is_confirmed
            status_message = 'xác nhận' if assignment.is_confirmed else 'chưa xác nhận'
        elif status_type == 'completed':
            assignment.is_completed = not assignment.is_completed
            status_message = 'hoàn thành' if assignment.is_completed else 'chưa hoàn thành'
        else:
            return JsonResponse({'error': 'Loại trạng thái không hợp lệ'}, status=400)

        assignment.save()

        return JsonResponse({
            'success': True,
            'message': f'Đã chuyển đổi trạng thái thành {status_message}',
            'is_confirmed': assignment.is_confirmed,
            'is_completed': assignment.is_completed
        })
    except DutyAssignment.DoesNotExist:
        return JsonResponse({'error': 'Phân công không tồn tại'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@can_view_schedules
def integrated_assignment(request):
    """Hiển thị giao diện phân công lịch trực tổng hợp"""
    # Lấy danh sách khoa/phòng ban
    if request.user.is_superuser or request.user.is_staff:
        departments = Department.objects.all().order_by('name')
    else:
        # Người dùng thường chỉ thấy khoa/phòng ban của họ
        departments = Department.objects.filter(id=request.user.profile.department_id) if request.user.profile.department else Department.objects.none()

    context = {
        'departments': departments,
        'department_id': request.user.profile.department_id if request.user.profile.department else None,
    }

    return render(request, 'schedules/integrated_assignment.html', context)


@login_required
@can_view_schedules
def get_integrated_schedule_data(request):
    """API để lấy dữ liệu cho bảng phân công lịch trực tổng hợp"""
    department_id = request.GET.get('department')
    date_range = request.GET.get('date_range')

    if not department_id or not date_range:
        return JsonResponse({'error': 'Thiếu thông tin khoa/phòng ban hoặc khoảng thời gian'}, status=400)

    try:
        department = Department.objects.get(pk=department_id)

        # Xử lý khoảng thời gian
        start_date_str, end_date_str = date_range.split(' - ')
        start_date = datetime.strptime(start_date_str, '%d/%m/%Y').date()
        end_date = datetime.strptime(end_date_str, '%d/%m/%Y').date()

        # Lấy danh sách ca trực
        shifts = DutyShift.objects.all().order_by('start_time')

        # Kiểm tra xem có ca trực nào không
        if not shifts.exists():
            return JsonResponse({
                'error': True,
                'message': 'Chưa có ca trực nào được tạo. Vui lòng tạo ít nhất một ca trực trước khi phân công.'
            }, status=400)

        # Lấy danh sách nhân viên thuộc khoa/phòng ban
        staff = User.objects.filter(profile__department=department, is_active=True).order_by('last_name', 'first_name')

        # Kiểm tra xem có nhân viên nào trong khoa/phòng ban không
        if not staff.exists():
            return JsonResponse({
                'error': True,
                'message': 'Không có nhân viên nào thuộc khoa/phòng ban này. Vui lòng thêm nhân viên vào khoa/phòng ban trước khi phân công.'
            }, status=400)

        # Tạo danh sách các ngày trong khoảng thời gian
        dates = []
        current_date = start_date

        # Danh sách tên các ngày trong tuần bằng tiếng Việt
        weekday_names = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']

        # Lấy danh sách ngày lễ
        holidays = Holiday.objects.filter(date__gte=start_date, date__lte=end_date).values_list('date', flat=True)
        holiday_dates = [holiday.strftime('%Y-%m-%d') for holiday in holidays]

        while current_date <= end_date:
            # Lấy tên ngày trong tuần (0 = Thứ 2, 6 = Chủ nhật)
            weekday = current_date.weekday()
            day_of_week = weekday_names[weekday]

            # Kiểm tra xem ngày hiện tại có phải là ngày lễ không
            is_holiday = current_date.strftime('%Y-%m-%d') in holiday_dates

            dates.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'formatted_date': current_date.strftime('%d/%m/%Y'),
                'day_of_week': day_of_week,
                'is_weekend': current_date.weekday() >= 5,  # 5 = Thứ 7, 6 = Chủ nhật
                'is_today': current_date == timezone.now().date(),
                'is_holiday': is_holiday
            })
            current_date += timedelta(days=1)

        # Kiểm tra xem đã có lịch trực nào cho khoa/phòng ban và khoảng thời gian này chưa
        existing_schedules = DutySchedule.objects.filter(
            department=department,
            start_date__lte=end_date,
            end_date__gte=start_date
        )

        # Lấy danh sách phân công hiện có
        assignments = {}
        for schedule in existing_schedules:
            schedule_assignments = DutyAssignment.objects.filter(
                schedule=schedule,
                date__gte=start_date,
                date__lte=end_date
            ).select_related('user', 'shift')

            for assignment in schedule_assignments:
                date_key = assignment.date.strftime('%Y-%m-%d')
                shift_key = assignment.shift.id

                if date_key not in assignments:
                    assignments[date_key] = {}

                if shift_key not in assignments[date_key]:
                    assignments[date_key][shift_key] = []

                assignments[date_key][shift_key].append({
                    'id': assignment.id,
                    'user_id': assignment.user.id,
                    'user_name': assignment.user.get_full_name() or assignment.user.username,
                    'notes': assignment.notes,
                    'is_holiday': assignment.is_holiday
                })

        # Chuẩn bị dữ liệu cho template
        shift_data = []
        for shift in shifts:
            shift_data.append({
                'id': shift.id,
                'name': shift.name,
                'start_time': shift.start_time.strftime('%H:%M'),
                'end_time': shift.end_time.strftime('%H:%M'),
                'color': shift.color
            })

        staff_data = []
        for user in staff:
            staff_data.append({
                'id': user.id,
                'name': user.get_full_name() or user.username
            })

        # Chuẩn bị dữ liệu phân công cho template
        for date_info in dates:
            date_key = date_info['date']
            for shift in shift_data:
                shift_key = shift['id']

                if date_key not in assignments:
                    assignments[date_key] = {}

                if shift_key not in assignments[date_key]:
                    assignments[date_key][shift_key] = []

                date_info['shifts'] = shift_data
                date_info['assignments'] = assignments.get(date_key, {}).get(shift_key, [])

        response_data = {
            'department': {
                'id': department.id,
                'name': department.name
            },
            'date_range': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            },
            'shifts': shift_data,
            'staff': staff_data,
            'dates': dates,
            'today': timezone.now().date().strftime('%Y-%m-%d')
        }

        return JsonResponse(response_data)

    except Department.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy khoa/phòng ban'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@can_create_schedules
def save_integrated_schedule(request):
    """API để lưu phân công lịch trực tổng hợp"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)

    try:
        department_id = request.POST.get('department')
        date_range = request.POST.get('date_range')
        assignments_json = request.POST.get('assignments')

        if not department_id or not date_range or not assignments_json:
            return JsonResponse({'error': 'Thiếu thông tin bắt buộc'}, status=400)

        department = Department.objects.get(pk=department_id)

        # Xử lý khoảng thời gian
        start_date_str, end_date_str = date_range.split(' - ')
        start_date = datetime.strptime(start_date_str, '%d/%m/%Y').date()
        end_date = datetime.strptime(end_date_str, '%d/%m/%Y').date()

        # Tạo tiêu đề tự động dựa trên khoa/phòng ban và khoảng thời gian
        auto_title = f"Lịch trực {department.name}: {start_date_str} - {end_date_str}"

        # Tạo hoặc cập nhật lịch trực
        schedule, created = DutySchedule.objects.get_or_create(
            department=department,
            start_date=start_date,
            end_date=end_date,
            defaults={
                'title': auto_title,
                'created_by': request.user,
                'is_published': True
            }
        )

        if not created:
            # Cập nhật lịch trực hiện có
            schedule.is_published = True
            schedule.save()

        # Xử lý phân công
        assignments = json.loads(assignments_json)

        for assignment_data in assignments:
            action = assignment_data.get('action')

            if action == 'add':
                # Thêm phân công mới
                user_id = assignment_data.get('user_id')
                date_str = assignment_data.get('date')
                shift_id = assignment_data.get('shift_id')

                if not user_id or not date_str or not shift_id:
                    continue

                user = User.objects.get(pk=user_id)
                shift = DutyShift.objects.get(pk=shift_id)
                assignment_date = datetime.strptime(date_str, '%Y-%m-%d').date()

                # Kiểm tra xem đã có phân công cho người dùng này vào ngày và ca trực này chưa
                existing_assignment = DutyAssignment.objects.filter(
                    schedule=schedule,
                    user=user,
                    date=assignment_date,
                    shift=shift
                ).first()

                if not existing_assignment:
                    # Lấy thông tin ngày lễ
                    is_holiday = assignment_data.get('is_holiday', False)

                    # Tạo phân công mới
                    DutyAssignment.objects.create(
                        schedule=schedule,
                        user=user,
                        date=assignment_date,
                        shift=shift,
                        is_confirmed=True,
                        is_holiday=is_holiday
                    )

                    # Tạo thông báo cho người dùng
                    DutyNotification.objects.create(
                        assignment=DutyAssignment.objects.get(
                            schedule=schedule,
                            user=user,
                            date=assignment_date,
                            shift=shift
                        ),
                        message=f'Bạn đã được phân công trực vào ngày {assignment_date.strftime("%d/%m/%Y")} ca {shift.name}',
                        sent_at=timezone.now()
                    )

            elif action == 'keep':
                # Giữ nguyên phân công hiện có
                assignment_id = assignment_data.get('id')
                if not assignment_id:
                    continue

                # Không cần làm gì, chỉ giữ nguyên phân công
                pass

        return JsonResponse({
            'success': True,
            'message': f'Đã lưu phân công lịch trực "{schedule.title}" thành công!',
            'schedule_id': schedule.id
        })

    except Department.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy khoa/phòng ban'}, status=404)
    except User.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy người dùng'}, status=404)
    except DutyShift.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy ca trực'}, status=404)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(f"Error in save_integrated_schedule: {str(e)}")
        print(error_traceback)
        return JsonResponse({'error': str(e), 'traceback': error_traceback}, status=500)


@login_required
def get_staff_by_department(request):
    """API để lấy danh sách nhân viên theo khoa/phòng ban"""
    department_id = request.GET.get('department_id')

    if not department_id:
        return JsonResponse({'staff': []})

    try:
        department = Department.objects.get(pk=department_id)
        users = User.objects.filter(profile__department=department, is_active=True).order_by('last_name', 'first_name')

        staff_list = []
        for user in users:
            staff_list.append({
                'id': user.id,
                'name': user.get_full_name() or user.username
            })

        return JsonResponse({'staff': staff_list})
    except Department.DoesNotExist:
        return JsonResponse({'staff': []}, status=404)


@login_required
@can_create_schedules
def save_holiday(request):
    """API để lưu thông tin ngày lễ"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)

    try:
        date_str = request.POST.get('date')
        name = request.POST.get('name')
        description = request.POST.get('description', '')

        if not date_str or not name:
            return JsonResponse({'error': 'Thiếu thông tin bắt buộc'}, status=400)

        # Chuyển đổi chuỗi ngày thành đối tượng date
        date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Tạo hoặc cập nhật ngày lễ
        holiday, _ = Holiday.objects.update_or_create(
            date=date,
            defaults={
                'name': name,
                'description': description
            }
        )

        # Cập nhật trạng thái ngày lễ cho tất cả các phân công vào ngày này
        DutyAssignment.objects.filter(date=date).update(is_holiday=True)

        return JsonResponse({
            'success': True,
            'message': 'Đã lưu thông tin ngày lễ thành công!',
            'holiday': {
                'id': holiday.id,
                'date': holiday.date.strftime('%Y-%m-%d'),
                'name': holiday.name
            }
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@can_create_schedules
def delete_holiday(request, holiday_id):
    """API để xóa ngày lễ"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)

    try:
        holiday = get_object_or_404(Holiday, pk=holiday_id)
        date = holiday.date
        holiday.delete()

        # Cập nhật trạng thái ngày lễ cho tất cả các phân công vào ngày này
        DutyAssignment.objects.filter(date=date).update(is_holiday=False)

        return JsonResponse({
            'success': True,
            'message': 'Đã xóa ngày lễ thành công!'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)