/**
 * tabulator_xml_func.js
 * <PERSON><PERSON><PERSON> các hàm tiện ích và biến cố định cho module XML4750
 * <PERSON><PERSON> gồ<PERSON> cả các hàm interface management
 */

// Biến toàn cục để lưu trữ dữ liệu danh mục
var categoryData = {};

// ===== UTILITY FUNCTIONS =====
/**
 * Format date from yyyyMMdd or yyyyMMddHHmm to dd/MM/yyyy or dd/MM/yyyy HH:mm
 */
function formatDisplayDate(dateStr) {
    if (!dateStr) return '';

    const str = dateStr.toString();

    if (str.length === 8) {
        // yyyyMMdd -> dd/MM/yyyy
        return `${str.substring(6,8)}/${str.substring(4,6)}/${str.substring(0,4)}`;
    } else if (str.length === 12) {
        // yyyyMMddHHmm -> dd/MM/yyyy HH:mm
        return `${str.substring(6,8)}/${str.substring(4,6)}/${str.substring(0,4)} ${str.substring(8,10)}:${str.substring(10,12)}`;
    }

    return dateStr; // Return as-is if format not recognized
}

// Định dạng số với dấu phân cách hàng nghìn
function formatNumber(num) {
    return new Intl.NumberFormat('vi-VN').format(num);
}

// ===== XML INTERFACE MANAGEMENT =====
// Track if data is from XML upload or database
let isDataFromXML = false;
let currentXMLData = null;

// ===== NOTIFICATION FUNCTIONS =====
function showLoading(message) {
    console.log("Loading:", message);
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Đang xử lý...',
            text: message || 'Vui lòng đợi...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }
}

function hideLoading() {
    console.log("Hide loading");
    if (typeof Swal !== 'undefined') {
        Swal.close();
    }
}

function showSuccess(message) {
    console.log("Success:", message);
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Thành công!',
            text: message,
            timer: 3000,
            showConfirmButton: false
        });
    } else {
        alert('Thành công: ' + message);
    }
}

function showError(message) {
    console.log("Error:", message);
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Lỗi!',
            text: message,
            confirmButtonText: 'OK'
        });
    } else {
        alert('Lỗi: ' + message);
    }
}

function showNotification(message, type) {
    if (typeof Swal !== 'undefined') {
        const iconMap = {
            'success': 'success',
            'error': 'error',
            'warning': 'warning',
            'info': 'info'
        };
        Swal.fire({
            icon: iconMap[type] || 'info',
            title: message,
            timer: 3000,
            showConfirmButton: false
        });
    } else {
        alert(message);
    }
}

// Main initialization function - Configuration only, no database connection
function initializeXMLInterface() {
    // Hide save button initially
    $('#savePreviewDataBtn').hide();

    // Set default date range (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    $('#filter_from_date').val(formatDateForInput(firstDay));
    $('#filter_to_date').val(formatDateForInput(today));

    // Set flag to prevent automatic data loading
    window.allowInitialTabulatorLoad = false;
}

// Tab title management
function updateTabTitles() {
    $('.nav-tabs .nav-link').each(function() {
        var $tab = $(this);
        var $shortTitle = $tab.find('.tab-short-title');
        var $fullTitle = $tab.find('.tab-full-title');

        if ($tab.hasClass('active')) {
            // Active tab: show full title
            $shortTitle.hide();
            $fullTitle.show();
        } else {
            // Inactive tab: show only short title
            $shortTitle.show();
            $fullTitle.hide();
        }
    });
}

// XML Upload Handler - Simplified with better debugging
function handleXMLUpload(files) {
    console.log("handleXMLUpload: Starting XML upload process...", files);

    if (files.length === 0) {
        console.warn("handleXMLUpload: No files provided");
        return;
    }

    // Clear previous XML data first
    console.log("handleXMLUpload: Clearing previous XML data...");
    isDataFromXML = false;
    currentXMLData = null;

    // Clear all tables before loading new data
    clearAllTables();

    // Show loading
    showLoading('Đang xử lý file XML...');

    // Check if parseXmlFileClientSide is available
    if (typeof parseXmlFileClientSide !== 'function') {
        console.error('handleXMLUpload: parseXmlFileClientSide function not available');
        hideLoading();
        showError('Chức năng xử lý XML chưa được tải! Vui lòng tải lại trang.');
        return;
    }

    console.log("handleXMLUpload: parseXmlFileClientSide is available, processing files...");

    const selectedGroup = 'XML0-15';
    const parsingPromises = [];

    // Process each file
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`handleXMLUpload: Processing file ${i + 1}/${files.length}: ${file.name} (${file.size} bytes)`);
        parsingPromises.push(parseXmlFileClientSide(file, selectedGroup));
    }

    Promise.all(parsingPromises)
        .then(async resultsArray => {
            console.log("handleXMLUpload: All files processed, results:", resultsArray);

            let aggregatedDataMap = {};
            let filesSuccessfullyProcessedWithData = 0;

            for (let i = 0; i < resultsArray.length; i++) {
                const results = resultsArray[i];
                console.log(`handleXMLUpload: Processing result ${i + 1}:`, results);

                // Check different possible result structures
                let dataToProcess = null;
                if (results && results.dataMap && Object.keys(results.dataMap).length > 0) {
                    dataToProcess = results.dataMap;
                    console.log("handleXMLUpload: Using results.dataMap");
                } else if (results && results.data && Object.keys(results.data).length > 0) {
                    dataToProcess = results.data;
                    console.log("handleXMLUpload: Using results.data");
                } else if (results && typeof results === 'object' && Object.keys(results).length > 0) {
                    // Check if results itself contains XML data
                    const hasXMLData = Object.keys(results).some(key => key.startsWith('XML'));
                    if (hasXMLData) {
                        dataToProcess = results;
                        console.log("handleXMLUpload: Using results directly");
                    }
                }

                if (dataToProcess) {
                    filesSuccessfullyProcessedWithData++;
                    console.log(`handleXMLUpload: File ${i + 1} has valid data:`, Object.keys(dataToProcess));

                    for (let xmlType in dataToProcess) {
                        if (!aggregatedDataMap[xmlType]) {
                            aggregatedDataMap[xmlType] = [];
                        }
                        const xmlData = dataToProcess[xmlType];
                        if (Array.isArray(xmlData)) {
                            aggregatedDataMap[xmlType] = aggregatedDataMap[xmlType].concat(xmlData);
                            console.log(`handleXMLUpload: Added ${xmlData.length} records to ${xmlType}`);
                        } else if (xmlData && typeof xmlData === 'object') {
                            aggregatedDataMap[xmlType].push(xmlData);
                            console.log(`handleXMLUpload: Added 1 record to ${xmlType}`);
                        }
                    }
                } else {
                    console.warn(`handleXMLUpload: File ${i + 1} has no valid data:`, results);
                }
            }

            hideLoading();

            console.log("handleXMLUpload: Final aggregated data:", aggregatedDataMap);
            console.log("handleXMLUpload: Files with data:", filesSuccessfullyProcessedWithData);

            if (filesSuccessfullyProcessedWithData > 0 && Object.keys(aggregatedDataMap).length > 0) {
                // Set XML preview mode
                currentXMLData = aggregatedDataMap;

                // Update data source state to XML import mode
                if (typeof updateDataSourceState === 'function') {
                    updateDataSourceState(DATA_SOURCE_TYPE.IMPORTED_XML);
                } else {
                    // Fallback
                    isDataFromXML = true;
                    $('#savePreviewDataBtn').show();
                }

                console.log("handleXMLUpload: Loading data into tables...");
                await loadDataIntoTables(aggregatedDataMap);

                showSuccess(`Đã xử lý thành công ${filesSuccessfullyProcessedWithData} file XML với ${Object.keys(aggregatedDataMap).length} loại dữ liệu!`);
            } else {
                console.error("handleXMLUpload: No valid data found in any files");
                showError('Không có dữ liệu hợp lệ nào được tìm thấy trong các file XML! Vui lòng kiểm tra định dạng file.');
            }
        })
        .catch(error => {
            hideLoading();
            console.error("handleXMLUpload: XML parsing failed:", error);
            showError('Lỗi khi xử lý file XML: ' + error.message);
        });
}

// Load data from database with filters
function loadFromDatabase() {
    const baseFilterData = {
        'object_type': $('#filter_object_type').val(),
        'kcb_type': $('#filter_kcb_type').val(),
        'time_type': $('#filter_time_type').val(),
        'from_date': $('#filter_from_date').val(),
        'to_date': $('#filter_to_date').val(),
        'page': 1,
        'size': -1,
    };
    showLoading('Đang tải dữ liệu từ CSDL...');
    // Load all XML types manually
    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    const loadPromises = [];

    xmlTypes.forEach(xmlType => {
        const filterData = {
            ...baseFilterData,
            'xml_type': xmlType
        };
        const promise = new Promise((resolve) => {
            $.ajax({
                url: '/xml4750/api/xml-data/',
                type: 'GET',
                data: filterData,
                success: function(response) {
                    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                        resolve({
                            xmlType: xmlType,
                            data: response.data,
                            summary: response.summary || null
                        });
                    } else {
                        resolve({
                            xmlType: xmlType,
                            data: [],
                            summary: response.summary || null
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error(`loadFromDatabase: ${xmlType} AJAX Error:`, {xhr: xhr, status: status, error: error});
                    console.error(`loadFromDatabase: ${xmlType} Response text:`, xhr.responseText);
                    resolve({ xmlType: xmlType, data: [], error: error });
                }
            });
        });

        loadPromises.push(promise);
    });

    Promise.all(loadPromises)
        .then(results => {
            hideLoading();

            // Aggregate data and summary
            const dataMap = {};
            const summaryMap = {};
            let totalRecords = 0;

            results.forEach(result => {
                if (result.data && result.data.length > 0) {
                    dataMap[result.xmlType] = result.data;
                    totalRecords += result.data.length;
                }

                // Store summary data if available
                if (result.summary) {
                    summaryMap[result.xmlType] = result.summary;
                }
            });

            if (totalRecords > 0) {
                // Set database mode
                if (typeof updateDataSourceState === 'function') {
                    updateDataSourceState(DATA_SOURCE_TYPE.DATABASE);
                } else {
                    // Fallback
                    isDataFromXML = false;
                    $('#savePreviewDataBtn').hide();
                }

                loadDataIntoTables(dataMap);

                // Update summaries with API data
                setTimeout(() => {
                    if (summaryMap['XML1'] && typeof updateXML1Summary === 'function') {
                        updateXML1Summary(summaryMap['XML1']);
                    }
                    if (summaryMap['XML2'] && typeof updateXML2Summary === 'function') {
                        updateXML2Summary(summaryMap['XML2']);
                    }
                    if (summaryMap['XML3'] && typeof updateXML3Summary === 'function') {
                        updateXML3Summary(summaryMap['XML3']);
                    }
                }, 1000);

                showSuccess(`Đã tải thành công ${totalRecords} bản ghi từ CSDL (${Object.keys(dataMap).length} loại XML)!`);
            } else {
                showError('Không có dữ liệu nào được tìm thấy với bộ lọc hiện tại');
            }
        })
        .catch(error => {
            console.error("loadFromDatabase: Error loading XML types:", error);
            hideLoading();
            showError('Lỗi khi tải dữ liệu: ' + error.message);
        });
}

// Utility function for date formatting
function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}

// Clear all tables data
function clearAllTables() {
    console.log("clearAllTables: Clearing all table data...");

    for (let i = 0; i <= 15; i++) {
        const xmlType = `XML${i}`;
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (tableElement && tableElement._tabulator) {
            try {
                tableElement._tabulator.clearData();
                console.log(`clearAllTables: Cleared ${xmlType} table`);
            } catch (error) {
                console.warn(`clearAllTables: Error clearing ${xmlType} table:`, error);
            }
        }
    }

    console.log("clearAllTables: All tables cleared");
}

// Get data from Tabulator tables (for XML preview mode)
function getDataFromTables() {
    console.log("getDataFromTables: Getting data from Tabulator tables...");

    const dataMap = {};
    let totalRecords = 0;

    for (let i = 0; i <= 15; i++) {
        const xmlType = `XML${i}`;
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (tableElement && tableElement._tabulator) {
            try {
                const tableData = tableElement._tabulator.getData();
                if (tableData && tableData.length > 0) {
                    dataMap[xmlType] = tableData;
                    totalRecords += tableData.length;
                    console.log(`getDataFromTables: ${xmlType} has ${tableData.length} records`);
                }
            } catch (error) {
                console.warn(`getDataFromTables: Error getting data from ${xmlType} table:`, error);
            }
        }
    }

    console.log(`getDataFromTables: Total ${totalRecords} records from ${Object.keys(dataMap).length} tables`);
    return dataMap;
}

// Export data from tables using export_xml.js
function exportDataFromTables(tableData) {
    console.log("exportDataFromTables: Starting export with data:", tableData);

    if (!tableData || Object.keys(tableData).length === 0) {
        showError('Không có dữ liệu để xuất');
        return;
    }

    // Check if export_xml.js functions are available
    if (typeof generateXmlFromModalData === 'function' && typeof downloadXMLFile === 'function') {
        try {
            console.log("exportDataFromTables: Using export_xml.js functions");

            // Use generateXmlFromModalData from export_xml.js
            const xmlContent = generateXmlFromModalData(tableData);

            if (!xmlContent || xmlContent.trim() === '') {
                showError('Không thể tạo nội dung XML từ dữ liệu');
                return;
            }

            // Create filename with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `XML4750_Export_${timestamp}.xml`;

            console.log("exportDataFromTables: Downloading file...");
            downloadXMLFile(xmlContent, filename);

            showSuccess(`File ${filename} đã được tạo và tải xuống thành công!`);
        } catch (error) {
            console.error("exportDataFromTables: Error creating XML:", error);
            showError('Lỗi khi tạo file XML: ' + error.message);
        }
    } else {
        console.error("exportDataFromTables: export_xml.js functions not available");
        console.log("exportDataFromTables: Available functions:", {
            generateXmlFromModalData: typeof generateXmlFromModalData,
            downloadXMLFile: typeof downloadXMLFile,
            createXMLContentForType: typeof createXMLContentForType
        });

        // Fallback: Use simple export
        exportDataAsJson(tableData);
    }
}

// Fallback export as JSON
function exportDataAsJson(tableData) {
    console.log("exportDataAsJson: Exporting as JSON fallback");

    try {
        const jsonContent = JSON.stringify(tableData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const filename = `XML4750_Export_${timestamp}.json`;

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showSuccess(`File ${filename} đã được tải xuống (định dạng JSON)!`);
    } catch (error) {
        console.error("exportDataAsJson: Error:", error);
        showError('Lỗi khi xuất dữ liệu: ' + error.message);
    }
}

// Fallback download function
function downloadXmlFile(xmlContent, filename) {
    console.log("downloadXmlFile: Downloading file:", filename);

    try {
        const blob = new Blob([xmlContent], { type: 'application/xml' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log("downloadXmlFile: File downloaded successfully");
    } catch (error) {
        console.error("downloadXmlFile: Error:", error);
        throw error;
    }
}

// Load data into Tabulator tables
async function loadDataIntoTables(dataMap) {

    if (!dataMap || typeof dataMap !== 'object') {
        console.error("loadDataIntoTables: Invalid data map provided");
        return;
    }

    // Check if we need to initialize tables first
    let needsInitialization = false;
    for (const xmlType in dataMap) {
        if (dataMap.hasOwnProperty(xmlType)) {
            const tableElementId = xmlType.toLowerCase() + '-table';
            const tableElement = document.getElementById(tableElementId);
            if (!tableElement || !tableElement._tabulator) {
                needsInitialization = true;
                break;
            }
        }
    }

    // Initialize tables only if needed
    if (needsInitialization) {
        if (window.TabulatorXMLTables && window.TabulatorXMLTables.initializeTablesConfigOnly) {
            try {
                await window.TabulatorXMLTables.initializeTablesConfigOnly();
            } catch (error) {
                console.error("loadDataIntoTables: Error initializing tables:", error);
                return;
            }
        } else {
            console.error("loadDataIntoTables: Cannot initialize tables - TabulatorXMLTables.initializeTablesConfigOnly not available");
            return;
        }
    }

    // Iterate through each XML type in the data map
    for (const xmlType in dataMap) {
        if (dataMap.hasOwnProperty(xmlType)) {
            const tableData = dataMap[xmlType];

            if (Array.isArray(tableData) && tableData.length > 0) {

                // Check if table exists and is initialized
                const tableElementId = xmlType.toLowerCase() + '-table';
                const tableElement = document.getElementById(tableElementId);

                if (!tableElement) {
                    console.warn(`loadDataIntoTables: Table element ${tableElementId} not found`);
                    continue;
                }

                if (!tableElement._tabulator) {
                    console.warn(`loadDataIntoTables: Table ${tableElementId} not initialized`);
                    continue;
                }

                // Load data into the corresponding table
                if (window.TabulatorXMLTables && window.TabulatorXMLTables.loadDataIntoTable) {
                    try {
                        await window.TabulatorXMLTables.loadDataIntoTable(xmlType, tableData);
                    } catch (error) {
                        console.error(`loadDataIntoTables: Error loading data into ${xmlType}:`, error);
                    }
                } else {
                    console.warn("loadDataIntoTables: TabulatorXMLTables.loadDataIntoTable not available");
                }
            } else {
                console.log(`loadDataIntoTables: No data for ${xmlType} (${Array.isArray(tableData) ? tableData.length : 'not array'} records)`);
            }
        }
    }
}

// Function to update summary information panel
function updateSummaryInfo() {
    try {
        // Get XML1 data for summary
        const xml1Table = document.getElementById('xml1-table');
        if (xml1Table && xml1Table._tabulator) {
            const xml1Data = xml1Table._tabulator.getData();
            if (xml1Data && xml1Data.length > 0) {
                const firstRecord = xml1Data[0];

                // Update basic info
                $('#summaryHoTen').text(firstRecord.hoTen || '-');
                $('#summaryNgaySinh').text(formatDisplayDate(firstRecord.ngaySinh) || '-');
                $('#summarySoCCCD').text(firstRecord.soCCCD || '-');
                $('#summaryNhomMau').text(firstRecord.nhomMau || '-');
                $('#summaryGioiTinh').text(firstRecord.gioiTinh === '1' ? 'Nam' : (firstRecord.gioiTinh === '2' ? 'Nữ' : '-'));
                $('#summaryMaQuocTich').text(firstRecord.maQuocTich || '-');

                // Calculate financial totals
                let totalThuoc = 0, totalVTYT = 0, totalChiBV = 0, totalChiBH = 0;

                xml1Data.forEach(record => {
                    totalThuoc += parseFloat(record.tThuoc || 0);
                    totalVTYT += parseFloat(record.tVTYT || 0);
                    totalChiBV += parseFloat(record.tTongChiBV || 0);
                    totalChiBH += parseFloat(record.tTongChiBH || 0);
                });

                // Update XML1 table footer with summary
                updateTabulatorFooter('xml1-table', {
                    totalRecords: xml1Data.length,
                    totalThuoc: totalThuoc,
                    totalVTYT: totalVTYT,
                    totalChiBV: totalChiBV,
                    totalChiBH: totalChiBH
                });

                // Chỉ update summary cho tab hiện tại để tránh lỗi table not found
                //updateXML2Summary();
                //updateXML3Summary();

                // Hide the old summary panel
                $('#summaryInfoPanel').hide();
            } else {
                // Clear footer if no data
                clearTabulatorFooter('xml1-table');
                $('#summaryInfoPanel').hide();
            }
        } else {
            clearTabulatorFooter('xml1-table');
            $('#summaryInfoPanel').hide();
        }
    } catch (error) {
        console.error('Error updating summary info:', error);
        $('#summaryInfoPanel').hide();
    }
}

// ===== TABULATOR FOOTER FUNCTIONS =====

/**
 * Cập nhật footer của Tabulator với thông tin tổng hợp
 */
function updateTabulatorFooter(tableId, summaryData) {
    const tableElement = document.getElementById(tableId);
    if (!tableElement || !tableElement._tabulator) {
        console.warn(`Table ${tableId} not found or not initialized`);
        return;
    }

    const table = tableElement._tabulator;

    // Tạo HTML cho footer
    let footerHtml = '';

    if (tableId === 'xml1-table') {
        footerHtml = `
            <div class="tabulator-footer-summary bg-white border-top p-2" style="border: 1px solid #dee2e6;">
                <div class="row text-dark">
                    <div class="col-md-2">
                        <strong>Tổng số:</strong> ${summaryData.totalRecords || 0} bản ghi
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng chi BV:</strong><br>
                        <span style="color: #28a745; font-weight: bold;">${formatNumber(summaryData.totalChiBV || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng chi BH:</strong><br>
                        <span style="color: #007bff; font-weight: bold;">${formatNumber(summaryData.totalChiBH || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng BHTT:</strong><br>
                        <span style="color: #17a2b8; font-weight: bold;">${formatNumber(summaryData.totalBHTT || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng BNCCT:</strong><br>
                        <span style="color: #ffc107; font-weight: bold;">${formatNumber(summaryData.totalBNCCT || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng BNTT:</strong><br>
                        <span style="color: #dc3545; font-weight: bold;">${formatNumber(summaryData.totalBNTT || 0)}</span>
                    </div>
                </div>
            </div>
        `;
    } else if (tableId === 'xml2-table') {
        footerHtml = `
            <div class="tabulator-footer-summary bg-white border-top p-2" style="border: 1px solid #dee2e6;">
                <div class="row text-dark">
                    <div class="col-md-2">
                        <strong>Tổng số:</strong> ${summaryData.totalRecords || 0} thuốc
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng thành tiền BV:</strong><br>
                        <span style="color: #28a745; font-weight: bold;">${formatNumber(summaryData.totalThanhTienBV || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng thành tiền BH:</strong><br>
                        <span style="color: #007bff; font-weight: bold;">${formatNumber(summaryData.totalThanhTienBH || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng BH thanh toán:</strong><br>
                        <span style="color: #17a2b8; font-weight: bold;">${formatNumber(summaryData.totalBHTT || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng thuốc (mã 4):</strong><br>
                        <span style="color: #6f42c1; font-weight: bold;">${formatNumber(summaryData.totalThuoc || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng máu (mã 7):</strong><br>
                        <span style="color: #e83e8c; font-weight: bold;">${formatNumber(summaryData.totalMau || 0)}</span>
                    </div>
                </div>
            </div>
        `;
    } else if (tableId === 'xml3-table') {
        footerHtml = `
            <div class="tabulator-footer-summary bg-white border-top p-2" style="border: 1px solid #dee2e6;">
                <div class="row text-dark">
                    <div class="col-md-2">
                        <strong>Tổng số:</strong> ${summaryData.totalRecords || 0} dịch vụ
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng thành tiền BV:</strong><br>
                        <span style="color: #28a745; font-weight: bold;">${formatNumber(summaryData.totalThanhTienBV || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng thành tiền BH:</strong><br>
                        <span style="color: #007bff; font-weight: bold;">${formatNumber(summaryData.totalThanhTienBH || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng BH thanh toán:</strong><br>
                        <span style="color: #17a2b8; font-weight: bold;">${formatNumber(summaryData.totalBHTT || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng DVKT (mã 1,2,3):</strong><br>
                        <span style="color: #fd7e14; font-weight: bold;">${formatNumber(summaryData.totalDVKT || 0)}</span>
                    </div>
                    <div class="col-md-2">
                        <strong>Tổng VTYT (mã 5,6,8,9,10,11):</strong><br>
                        <span style="color: #20c997; font-weight: bold;">${formatNumber(summaryData.totalVTYT || 0)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // Tìm hoặc tạo footer element
    let footerElement = tableElement.querySelector('.tabulator-footer-summary');
    if (!footerElement) {
        // Tạo footer mới
        footerElement = document.createElement('div');
        footerElement.className = 'tabulator-footer-summary';

        // Thêm vào cuối table
        const tabulatorElement = tableElement.querySelector('.tabulator');
        if (tabulatorElement) {
            tabulatorElement.appendChild(footerElement);
        }
    }

    // Cập nhật nội dung
    footerElement.innerHTML = footerHtml;
}

/**
 * Xóa footer của Tabulator
 */
function clearTabulatorFooter(tableId) {
    const tableElement = document.getElementById(tableId);
    if (!tableElement) return;

    const footerElement = tableElement.querySelector('.tabulator-footer-summary');
    if (footerElement) {
        footerElement.remove();
    }
}

// Make functions available globally
window.handleXMLUpload = handleXMLUpload;
window.loadFromDatabase = loadFromDatabase;
window.loadDataIntoTables = loadDataIntoTables;
window.clearAllTables = clearAllTables;
window.getDataFromTables = getDataFromTables;
window.updateDataSourceState = updateDataSourceState;
window.exportDataFromTables = exportDataFromTables;
window.exportDataAsJson = exportDataAsJson;

if (typeof window.downloadXmlFile === 'undefined') {
    window.downloadXmlFile = downloadXmlFile;
}

// ===== END XML INTERFACE MANAGEMENT =====

// ===== QUẢN LÝ TRẠNG THÁI EDIT =====
let editedRows = new Set(); // Lưu các row đã được edit

const maCSKCB = '64020'
const tuyenbv = '2'

const gioi_tinh = {"1": "Nam", "2": "Nữ", "3": "Chưa xác định"}       //Giới tính cho combobox
// Mã loại khám chữa bệnh được quy định theo quyết định 824/QĐ-BYT
const ma_loai_kcb = {"01":"Khám bệnh.","02":"Điều trị ngoại trú.","03":"Điều trị nội trú.","04":"Điều trị nội trú ban ngày.",
    "05":"Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm, có khám bệnh và lĩnh thuốc.",
    "06":"Điều trị lưu tại Trạm Y tế tuyến xã, Phòng khám đa khoa khu vực.","07":"Nhận thuốc theo hẹn (không khám bệnh).",
    "08":"Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm, có thực hiện các dịch vụ kỹ thuật và/hoặc được sử dụng thuốc.",
    "09":"Điều trị nội trú dưới 04 (bốn) giờ.",
    "10":"Các trường hợp khác."}

// Định nghĩa mã đối tượng
const ma_doi_tuong = {
    "01": "Đúng tuyến", "1": "Đúng tuyến", "1.1": "Đúng tuyến ban đầu", "1.2": "Đúng tuyến tại tỉnh",
    "1.3": "Chuyển tuyến", "1.4": "Công tác, làm việc lưu động", "1.5": "Tái khám", "1.6": "Hiến tạng",
    "1.7": "Trẻ sơ sinh", "1.8": "Bệnh lao", "1.9": "HIV/AIDS", "1.10": "COVID-19", "02": "Cấp cứu",
    "2": "Cấp cứu", "03": "Trái tuyến", "3": "Trái tuyến", "3.1": "Trái tuyến TW", "3.2": "Trái tuyến Tỉnh",
    "3.3": "Trái tuyến tuyến huyện", "3.4": "Trái tuyến ngoại trú TW", "3.5": "Trái tuyến ngoại trú Tỉnh",
    "3.6": "Dân tộc, vùng khó khăn", "3.7": "Còn lại",
    "7": "Lĩnh thuốc theo giấy hẹn trong trường hợp dịch bệnh hoặc bất khả kháng hoặc do bất kỳ nguyên nhân nào",
    "7.1": "Người bệnh đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc.",
    "7.2": "Người bệnh ủy quyền cho người khác đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc.",
    "7.3": "Cơ sở khám bệnh, chữa bệnh chuyển thuốc cho cơ sở khám bệnh, chữa bệnh khác.",
    "7.4": "Cơ sở khám bệnh, chữa bệnh chuyển thuốc đến cho người bệnh.",
    "8": "Thu hồi đề nghị thanh toán",
    "9": "Khám bệnh, chữa bệnh dịch vụ"
};

const ma_dan_toc = {'01': 'Kinh ','02': 'Tày','03': 'Thái','04': 'Hoa ','05': 'Khmer','06': 'Mường ','07': 'Nùng  ','08': 'Mông','09': 'Dao','10': 'Gia Rai','11': 'Ngái','12': 'Ê  Đê','13': 'Ba Na','14': 'Xơ Đăng','15': 'Sán Chay ','16': 'Cơ Ho','17': 'Chăm ','18': 'Sán Dìu','19': 'Hrê','20': 'Mnông','21': 'Raglay','22': 'Xtiêng','23': 'Bru Vân Kiều','24': 'Thổ (4)','25': 'Giáy','26': 'Cơ Tu','27': 'Gié Triêng','28': 'Mạ','29': 'Khơ mú','30': 'Co','31': 'Tà Ôi','32': 'Chơ Ro','33': 'Kháng','34': 'Xinh Mun','35': 'Hà Nhì','36': 'Chu Ru','37': 'Lào','38': 'La Chí','39': 'La Ha','40': 'Phù Lá','41': 'La Hủ','42': 'Lự','43': 'Lô Lô','44': 'Chứt','45': 'Mảng','46': 'Pà Thẻn','47': 'Cơ Lao','48': 'Cống','49': 'Bố Y','50': 'Si La','51': 'Pu Péo','52': 'Brâu','53': 'Ơ Đu','54': 'Rơ Măm','55': 'Người nước ngoài','56': 'Không xác định'}
const trang_thai_gui_hs = {0: 'Chưa gửi', 1: 'Đã gửi'}
const thang = {'1': 'Tháng 1', '2': 'Tháng 2', '3': 'Tháng 3', '4': 'Tháng 4', '5': 'Tháng 5', '6': 'Tháng 6', '7': 'Tháng 7', '8': 'Tháng 8', '9': 'Tháng 9', '10': 'Tháng 10', '11': 'Tháng 11', '12': 'Tháng 12'}
const nam = {'2020': 'Năm 2020', '2021': 'Năm 2021', '2022': 'Năm 2022', '2023': 'Năm 2023', '2024': 'Năm 2024', '2025': 'Năm 2025', '2026': 'Năm 2026', '2027': 'Năm 2027', '2028': 'Năm 2028', '2029': 'Năm 2029', '2030': 'Năm 2030', '2031': 'Năm 2031', '2032': 'Năm 2032', '2033': 'Năm 2033', '2034': 'Năm 2034', '2035': 'Năm 2035', '2036': 'Năm 2036', '2037': 'Năm 2037', '2038': 'Năm 2038', '2039': 'Năm 2039', '2040': 'Năm 2040'}

const key_loai_kcb_ngoai_tru = ["01", "07"]
const key_loai_kcb_dieu_tri_ngoai_tru = ["02", "05", "06", "08"]
const key_loai_kcb_noi_tru = ["03", "04", "09"]

const ma_loi_cong_bhxhvn = {
    '000':'Thông tin thẻ BHYT chính xác',
    '001':'Thẻ BHYT do BHXH Bộ Quốc phòng quản lý, đề nghị kiểm tra thẻ BHYT và thông tin giấy tờ tùy thân',
    '002':'Thẻ BHYT do BHXH Bộ Công an quản lý, đề nghị kiểm tra thẻ BHYT và thông tin giấy tờ tùy thân',
    '003':'Thẻ BHYT cũ hết giá trị sử dụng, được cấp thẻ mới',
    '004':'Thẻ BHYT cũ còn giá rị sử dụng, được cấp thẻ mới',
    '010':'Thẻ BHYT hết giá trị sử dụng',
    '051':'Mã thẻ BHYT không đúng',
    '052':'Mã tỉnh cấp thẻ BHYT (ký tự thứ 4,5) của thẻ BHYT không đúng',
    '053':'Mã quyền lợi BHYT (ký tự thứ 3) của thẻ BHYT không đúng',
    '050':'Không có thông tin thẻ BHYT',
    '060':'Thẻ BHYT sai họ tên',
    '061':'Thẻ BHYT sai họ tên (đúng ký tự đầu)',
    '070':'Thẻ BHYT sai ngày sinh',
    '100':'Lỗi khi lấy dữ liệu sổ thẻ',
    '101':'Lỗi server',
    '110':'Thẻ BHYT đã thu hồi',
    '120':'Thẻ BHYT đã báo giảm',
    '121':'Thẻ BHYT đã báo giảm chuyển ngoại tỉnh',
    '122':'Thẻ BHYT đã báo giảm chuyển nội tỉnh',
    '123':'Thẻ BHYT đã báo giảm do tăng lại cùng đơn vị',
    '124':'Thẻ BHYT đã báo giảm ngừng tham gia',
    '130':'Trẻ em không xuất trình thẻ BHYT',
    '205':'Lỗi sai định dạng tham số',
    '401':'Lỗi xác thực tài khoản',
    '054':'Số CCCD của cán bộ thực hiện tra cứu không tồn tại trong danh sách người sử dụng do CSKCB đăng ký',
    '055':'Họ và tên của cán bộ thực hiện tra cứu không khớp với số CCCD',
}

const ma_kv = ["", "K1", "K2","K3"]   // Mã khu vực đối với bệnh nhân thuộc khu vực khó khăn
const ma_kv_dict = {"":"","K1":"K1", "K2":"K2","K3":"K3"}   // Mã khu vực đối với bệnh nhân thuộc khu vực khó khăn

const ma_nhom = {"1": "Xét nghiệm" ,
    "2": "Chẩn đoán hình ảnh",
    "3": "Thăm dò chức năng",
    "4": "Thuốc trong danh mục BHYT",
    "5": "Thuốc ngoài danh mục BHYT",
    "6": "Thuốc thanh toán theo tỷ lệ",
    "7": "Máu",
    "8": "Phẫu thuật",
    "9": "Dịch vụ kỹ thuật (DVKT) thanh toán theo tỷ lệ",
    "10": "Vật tư y tế trong danh mục BHYT",
    "11": "Vật tư y tế (VTYT) thanh toán theo tỷ lệ",
    "12": "Vận chuyển",
    "13": "Khám bệnh",
    "14": "Ngày giường bệnh ban ngày",
    "15": "Ngày giường bệnh điều trị nội trú",
    "16": "Ngày giường lưu",
    "17": "Chế phẩm máu",
    "18": "Thủ thuật",
    "19": "Còn lại",
}

const ma_nhom_thuoc = ["4", "5", "6","7", "17", "19"]
const ma_nhom_dvkt = ["1","2","3","8","9","10","11","12","13","14","15","16","18","19"]

const duong_dung = {'':'','1.01': 'Uống' ,
    '1.02': 'Ngậm' ,
    '1.03': 'Nhai' ,
    '1.04': 'Đặt dưới lưỡi' ,
    '1.05': 'Ngậm dưới lưỡi' ,
    '2.01': 'Tiêm bắp' ,
    '2.02': 'Tiêm dưới da' ,
    '2.03': 'Tiêm trong da' ,
    '2.04': 'Tiêm tĩnh mạch' ,
    '2.05': 'Tiêm truyền tĩnh mạch' ,
    '2.06': 'Tiêm vào ổ khớp' ,
    '2.07': 'Tiêm nội nhãn cầu' ,
    '2.08': 'Tiêm trong dịch kính của mắt' ,
    '2.09': 'Tiêm vào các khoang của cơ thể' ,
    '2.10': 'Tiêm' ,
    '2.11': 'Tiêm động mạch khối u' ,
    '2.12': 'Tiêm vào khoang tự nhiên' ,
    '2.13': 'Tiêm vào khối u' ,
    '2.14': 'Truyền tĩnh mạch' ,
    '2.15': 'Tiêm truyền' ,
    '3.01': 'Bôi' ,
    '3.02': 'Xoa ngoài' ,
    '3.03': 'Dán trên da' ,
    '3.04': 'Xịt ngoài da' ,
    '3.05': 'Dùng ngoài' ,
    '4.01': 'Đặt âm đạo' ,
    '4.02': 'Đặt hậu môn' ,
    '4.03': 'Thụt hậu môn - trực tràng' ,
    '4.04': 'Đặt' ,
    '4.05': 'Đặt tử cung' ,
    '4.06': 'Thụt' ,
    '5.01': 'Phun mù' ,
    '5.02': 'Dạng hít' ,
    '5.03': 'Bột hít' ,
    '5.04': 'Xịt' ,
    '5.05': 'Khí dung' ,
    '5.06': 'Đường hô hấp' ,
    '5.07': 'Xịt mũi' ,
    '5.08': 'Xịt họng' ,
    '5.09': 'Thuốc mũi' ,
    '5.10': 'Nhỏ mũi' ,
    '6.01': 'Nhỏ mắt' ,
    '6.02': 'Tra mắt' ,
    '6.03': 'Thuốc mắt' ,
    '6.04': 'Nhỏ tai' ,
    '9.01': 'Áp ngoài da' ,
    '9.02': 'Áp sát khối u' ,
    '9.03': 'Bình khí lỏng hoặc nén' ,
    '9.04': 'Bình khí nén' ,
    '9.05': 'Bôi trực tràng' ,
    '9.06': 'Đánh tưa lưỡi' ,
    '9.07': 'Cấy vào khối u' ,
    '9.08': 'Chiếu ngoài' ,
    '9.09': 'Dung dịch' ,
    '9.10': 'Dung dịch rửa' ,
    '9.11': 'Dung dịch thẩm phân' ,
    '9.12': 'Phun' ,
    '9.13': 'Túi' ,
    '9.14': 'Hỗn dịch' ,
    '9.15': 'Bột đông khô để pha hỗn dịch' ,
}

const ma_tai_nan = { "0": "Không xác định", "1": "Tai nạn giao thông", "2": "Tai nạn lao động", "3": "Bỏng", "4": "Bạo lực, xung đột", "5": "Tự tử", "6": "Ngộ độc các loại", "7": "Khác" }

const ket_qua_dtri = {
    "1": "Khỏi",
    "2": "Đỡ",
    "3": "Không thay đổi",
    "4": "Nặng hơn",
    "5": "Tử vong",
    "6": "Tiên lượng nặng xin về",
    "7": "Chưa xác định",
    "8": "Tử vong ngoại viện",
}

const ma_loai_rv = {
    "1": "Ra viện",
    "2": "Chuyển tuyến theo yêu cầu chuyên môn",
    "3": "Trốn viện",
    "4": "Xin ra viện",
    "5": "Chuyển tuyến theo yêu cầu người bệnh",
}

const pham_vi = {"1": "Trong phạm vi hưởng BHYT", "2": "Ngoài phạm vi hưởng BHYT", "3":"Ngoài danh mục do quỹ BHYT chi trả"}
const ma_pttt = {"1": "Phí dịch vụ", "2": "Định suất", "3": "Trường hợp bệnh (DRG)"}
const nguon_ctra = {"1": "Quỹ BHYT chi trả", "2": "Thuốc của dự án hoặc viện trợ", "3": "Thuốc thuộc chương trình mục tiêu Quốc gia", "4": "Các nguồn khác chi trả"}
const pp_vo_cam = {"":"","1": "Gây mê","2": "Gây tê","3": "Châm tê","4": "Các phương pháp khác"}


// Hàm load dữ liệu danh mục
function loadCategoryData(categories, formats) {
    var promises = [];

    // Tạo promise cho mỗi danh mục cần load
    categories.forEach(function(category, index) {
        // Lấy định dạng tương ứng nếu có
        var format = formats && formats[category] ? formats[category] : 'ma_diengiai';

        if (category === 'quanhuyen' || category === 'xaphuong' || category === 'tinh') {
            url = API_OTHER_CATEGORY_DATA_URL;
        } else {
            url = API_CATEGORY_DATA_URL;
        }
        var promise = $.ajax({
            url: url,
            type: "GET",
            data: {
                category: category,
                active_only: true,
                format: format
            },
            dataType: "json",
            cache: true
        }).done(function(response) {
            if (response.success && Array.isArray(response.data)) {
                let processedData = [];
                if (category === 'tinh') {
                    processedData = response.data.map(item => ({
                        value: item.ma_tinh,
                        label: `${item.ma_tinh} - ${item.ten_tinh}`
                    }));
                } else if (category === 'quanhuyen') {
                    processedData = response.data.map(item => ({
                        value: item.ma_quan_huyen,
                        label: `${item.ma_quan_huyen} - ${item.ten_quan_huyen}`,
                        // Giả định item.ma_tinh là một object Tinh từ Django ORM
                        tinh_id: item.ma_tinh && item.ma_tinh.ma_tinh ? item.ma_tinh.ma_tinh : (typeof item.ma_tinh === 'string' ? item.ma_tinh : null)
                    }));
                } else if (category === 'xaphuong') {
                    processedData = response.data.map(item => ({
                        value: item.ma_xa_phuong,
                        label: `${item.ma_xa_phuong} - ${item.ten_xa_phuong}`,
                        // Giả định item.ma_quan_huyen và item.ma_tinh là các object từ Django ORM
                        huyen_id: item.ma_quan_huyen && item.ma_quan_huyen.ma_quan_huyen ? item.ma_quan_huyen.ma_quan_huyen : (typeof item.ma_quan_huyen === 'string' ? item.ma_quan_huyen : null),
                        tinh_id: item.ma_tinh && item.ma_tinh.ma_tinh ? item.ma_tinh.ma_tinh : (typeof item.ma_tinh === 'string' ? item.ma_tinh : null)
                    }));
                } else {
                    // Đối với các danh mục khác (ví dụ từ API_CATEGORY_DATA_URL)
                    // mà đã trả về đúng định dạng {value: ..., label: ...} thì giữ nguyên
                    processedData = response.data;
                }
                categoryData[category] = processedData;
            } else {
                console.error("Lỗi khi lấy danh sách " + category + ":", response.error);
                categoryData[category] = [];
            }
        }).fail(function(xhr, status, error) {
            console.error("Lỗi AJAX khi load " + category + ":", error);
            categoryData[category] = [];
        });

        promises.push(promise);
    });

    // Trả về promise tổng hợp
    return $.when.apply($, promises);
}

// Hàm lấy dữ liệu danh mục đã load
function getCategoryValues(category) {
    // Trả về dữ liệu dưới dạng object với key là value và value là label
    // Đây là định dạng mà Tabulator mong đợi cho editor "list"
    var values = {};
    var data = categoryData[category] || [];

    data.forEach(function(item) {
        values[item.value] = item.label;
    });

    return values;
}

// Custom datetime editor: sử dụng <input type="datetime-local">
const dateTimeEditor = function(cell, onRendered, success, cancel, editorParams){
    const editor = document.createElement("input");
    editor.setAttribute("type", "datetime-local");

    editor.style.padding = "3px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";

    const originalValue = cell.getValue(); // ví dụ: "200012251230"

    // Chuyển từ yyyyMMddHHmm -> yyyy-MM-dd'T'HH:mm (định dạng dùng cho input)
    const parsed = luxon.DateTime.fromFormat(originalValue, "yyyyMMddHHmm");
    editor.value = parsed.isValid ? parsed.toFormat("yyyy-MM-dd'T'HH:mm") : "";

    onRendered(() => {
        editor.focus();
    });

    function successFunc(){
        const selected = editor.value; // ví dụ: "2000-12-25T12:30"
        const originalValue = cell.getValue();
        if (selected) {
            const dt = luxon.DateTime.fromFormat(selected, "yyyy-MM-dd'T'HH:mm");
            let newValue;
            if (dt.isValid) {
                newValue = dt.toFormat("yyyyMMddHHmm");
            } else {
                newValue = dt.toFormat("yyyyMMddHHmm"); // Giữ nguyên hành vi có thể lưu "Invalid DateTime"
            }

            console.log(`[dateTimeEditor] successFunc: Original='${originalValue}', New Attempt='${newValue}', Selected Input='${selected}'`);
            if (newValue !== originalValue) {
                console.log("[dateTimeEditor] Calling success() with:", newValue);
                success(newValue);
            } else {
                console.log("[dateTimeEditor] New value is same as original. Calling cancel().");
                cancel();  // hoặc không gọi gì cả
            }
        } else {
            // Người dùng đã xóa giá trị trong input
            console.log(`[dateTimeEditor] successFunc: Input is empty. Original='${originalValue}'`);
            if (cell.getValue()) {
                console.log("[dateTimeEditor] Original had value, input empty. Calling success('')");
                success(""); // từ có giá trị chuyển về rỗng
            } else {
                console.log("[dateTimeEditor] Original was also empty. Calling cancel().");
                cancel(); // giữ nguyên
            }
        }
    }

    editor.addEventListener("blur", successFunc);
    editor.addEventListener("keydown", function(e) {
        if (e.key === "Enter") {
            successFunc();
        }
    });

    return editor;
};

// Custom date editor
const dateEditor = function(cell, onRendered, success, cancel, editorParams){
    const editor = document.createElement("input");
    editor.setAttribute("type", "date");

    editor.style.padding = "3px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";

    const originalValue = cell.getValue(); // ví dụ: "20001225"

    // Chuyển từ yyyyMMdd -> yyyy-MM-dd (định dạng dùng cho input)
    const parsed = luxon.DateTime.fromFormat(originalValue, "yyyyMMdd");
    editor.value = parsed.isValid ? parsed.toFormat("yyyy-MM-dd") : "";

    onRendered(() => {
        editor.focus();
    });

    function successFunc(){
        const selected = editor.value; // ví dụ: "2000-12-25T12:30"
        const originalValue = cell.getValue();
        if (selected) {
            const dt = luxon.DateTime.fromFormat(selected, "yyyy-MM-dd");
            let newValue;
            if (dt.isValid) {
                newValue = dt.toFormat("yyyyMMdd");
            } else {
                newValue = dt.toFormat("yyyyMMdd"); // Giữ nguyên hành vi
            }
            console.log(`[dateEditor] successFunc: Original='${originalValue}', New Attempt='${newValue}', Selected Input='${selected}'`);
            if (newValue !== originalValue) {
                console.log("[dateEditor] Calling success() with:", newValue);
                success(newValue);
            } else {
                console.log("[dateEditor] New value is same as original. Calling cancel().");
                cancel();
            }
        } else {
            // Người dùng đã xóa giá trị trong input
            console.log(`[dateEditor] successFunc: Input is empty. Original='${originalValue}'`);
            if (cell.getValue()) { // Nếu cell ban đầu có giá trị
                console.log("[dateEditor] Original had value, input empty. Calling success('')");
                success(""); // Lưu giá trị rỗng
            } else { // Cell ban đầu cũng rỗng
                console.log("[dateEditor] Original was also empty. Calling cancel().");
                cancel(); // Không có thay đổi
            }
        }
    }

    editor.addEventListener("blur", successFunc);
    editor.addEventListener("keydown", function(e) {
        if (e.key === "Enter") {
            successFunc();
        }
    });

    return editor;
};

// Removed duplicate formatDateTime and formatDate functions - using the ones below

const safeListEditor = function(cell, onRendered, success, cancel, editorParams){
    // Chỉ dùng cho cell, không dùng cho filter
    var editor = Tabulator.editors.list(cell, onRendered, function(value){
        if (value === "" || value === undefined) {
            cancel();
        } else {
            success(value);
        }
    }, cancel, editorParams);
    return editor;
};

// Hàm formatter cho các trường key + value
function formatKeyValue(dict) {
    return function(cell) {
        const value = cell.getValue();
        // Kiểm tra chính xác cho null, undefined và empty string
        if (value === null || value === undefined || value === "") {
            console.log(`[formatKeyValue] value is empty or undefined for cell with value: ${cell.getValue()}`);
            return ""; 
        }
            
        const label = dict[value];
        return `${value} - ${label}`;
    };
}

// Xử lý dữ liệu từ jQuery trước khi đưa vào Tabulator
function processData(data) {
    return data.map(function(row) {
        if (row.ketLuan && typeof row.ketLuan === 'string') {
            row.ketLuan = row.ketLuan
                .replace(/\t/g, '')                    // Loại bỏ tab
                .replace(/\r\n/g, '\n').replace(/\r/g, '\n') // Chuẩn hóa xuống dòng
                .split('\n').map(line => line.trim()).join('\n') // Trim từng dòng
                .replace(/\n\s*\n\s*\n/g, '\n\n')     // Chỉ giữ tối đa 1 dòng trống
                .trim();                               // Trim tổng thể
        }
        return row;
    });
}

// Formatter functions
function formatDateTime(cell, formatterParams, onRendered) {
    const value = cell.getValue();
    if (!value) return '';

    // Nếu giá trị đã ở định dạng dd/MM/yyyy HH:mm thì giữ nguyên
    if (value.includes('/') && value.includes(':')) {
        return value;
    }

    // Nếu giá trị ở định dạng YYYYMMDDHHmm thì chuyển đổi
    if (value.length === 12 && /^\d{12}$/.test(value)) {
        const year = value.substring(0, 4);
        const month = value.substring(4, 6);
        const day = value.substring(6, 8);
        const hour = value.substring(8, 10);
        const minute = value.substring(10, 12);
        return `${day}/${month}/${year} ${hour}:${minute}`;
    }

    return value;
}

function formatDate(cell, formatterParams, onRendered) {
    const value = cell.getValue();
    if (!value) return '';

    // Nếu giá trị đã ở định dạng dd/MM/yyyy thì giữ nguyên
    if (value.includes('/')) {
        return value;
    }

    // Nếu giá trị ở định dạng YYYYMMDD thì chuyển đổi
    if (value.length === 8 && /^\d{8}$/.test(value)) {
        const year = value.substring(0, 4);
        const month = value.substring(4, 6);
        const day = value.substring(6, 8);
        return `${day}/${month}/${year}`;
    }

    return value;
}

// Function to handle saving edited cell data via AJAX
function saveEditedCell(cell) {
    var rowData = cell.getRow().getData();
    var field = cell.getColumn().getField();
    var value = cell.getValue();
    var xmlKey = cell.getTable().element.id.replace('-table', '');

    console.log("Saving cell edit for", xmlKey, ":", field, "=", value);
    console.log("Full row data:", rowData);

    // Lấy CSRF token từ cookie
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');

    // Gửi AJAX request để lưu dữ liệu
    $.ajax({
        url: '/xml4750/update_field/',
        method: 'POST',
        data: {
            xml_type: xmlKey.toUpperCase(),
            row_id: rowData.id,
            field: field,
            value: value
        },
        headers: {
            'X-CSRFToken': csrftoken
        },
        success: function(response) {
            if (response.success) {
                console.log('Cập nhật thành công:', response);
                // Hiển thị thông báo thành công
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công',
                    text: 'Dữ liệu đã được cập nhật',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            } else {
                console.error('Cập nhật thất bại:', response);
                // Hiển thị thông báo lỗi
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: response.message || 'Đã xảy ra lỗi khi cập nhật dữ liệu',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Lỗi AJAX:', error);
            // Hiển thị thông báo lỗi
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Đã xảy ra lỗi khi cập nhật dữ liệu: ' + error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }
    });
}

// Hàm thực hiện việc lưu dữ liệu thông qua ajax
function saveRowData(row) {
    var rowData = row.getData();
    var xmlKey = row.getTable().element.id.replace('-table', '');
    var saveButton = $(row.getElement()).find('.save-row-btn');

    console.log("Saving row data for", xmlKey, ":", rowData);

    // Disable save button during AJAX call
    saveButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

    // Lấy CSRF token từ cookie (assuming getCookie is available in tabulator_xml_func.js)
    const csrftoken = getCookie('csrftoken');

    // Gửi AJAX request để lưu dữ liệu
    $.ajax({
        url: '/xml4750/save_row/', // New endpoint
        method: 'POST',
        data: {
            xml_type: xmlKey.toUpperCase(),
            row_data: JSON.stringify(rowData) // Send the whole row data as JSON string
        },
        headers: {
            'X-CSRFToken': csrftoken
        },
        success: function(response) {
            if (response.success) {
                console.log('Lưu dòng thành công:', response);
                // Hiển thị thông báo thành công
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công',
                    text: 'Dữ liệu dòng đã được lưu',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
                // Hide the save button and clear edited state indicator
                saveButton.hide();
                row.reformat(); // This should clear the edited state indicator
            } else {
                console.error('Lưu dòng thất bại:', response);
                // Hiển thị thông báo lỗi
                Swal.fire('Lỗi!', response.message || 'Đã xảy ra lỗi khi lưu dữ liệu dòng', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Lỗi AJAX khi lưu dòng:', error);
            Swal.fire('Lỗi!', 'Đã xảy ra lỗi khi lưu dữ liệu dòng: ' + error, 'error');
        },
        complete: function() {
            // Re-enable button regardless of success/error
            saveButton.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu');
        }
    });
}

// ===== HÀM CHUNG ĐỂ ÁP DỤNG CELL EVENTS =====
function createCellEventHandlers(onEditedCallback) { // Thêm callback
    let oldValues = {};  // dùng để lưu giá trị cũ tạm thời
    let isRestoringCell = false;

    // Hàm lấy key ổn định cho mỗi cell
    function getCellKey(cell) {
        const row = cell.getRow();
        const field = cell.getField();
        const data = row.getData();
        // Ưu tiên id, sau đó position, cuối cùng random fallback
        let rowKey = (data && data.id) || (typeof row.getPosition === "function" ? row.getPosition() : undefined);
        if (rowKey === undefined || rowKey === null) {
            rowKey = Math.random().toString(36).substr(2, 9);
        }
        return `${rowKey}-${field}`;
    }

    return {
        cellEditing: function(cell) {
            // LƯU GIÁ TRỊ CŨ TRƯỚC KHI EDIT
            const key = getCellKey(cell);
            const currentValue = cell.getValue();
            
            // Chỉ lưu nếu chưa có trong oldValues (tránh ghi đè)
            if (oldValues[key] === undefined) {
                oldValues[key] = currentValue;
                console.log(`Cell ${key} editing started. Old value saved: '${currentValue}'`);
            }
        },
        
        cellEdited: function(cell) {
            const key = getCellKey(cell);
            const row = cell.getRow();
            const field = cell.getField();
            const newValue = cell.getValue();
            const oldValue = oldValues[key]; // Bây giờ sẽ có giá trị

            const columnDef = cell.getColumn().getDefinition();
            const editorParams = columnDef.editorParams;

            // Nếu đang phục hồi thì bỏ qua
            if (isRestoringCell) {
                isRestoringCell = false;
                delete oldValues[key];
                return;
            }
        
            // Nếu giá trị không thay đổi, không làm gì cả
            if (newValue === oldValue) {
                delete oldValues[key];
                console.log(`Cell ${key} - No change detected (both values: '${newValue}')`);
                return;
            }
        
            // Nếu giá trị mới là rỗng hoặc undefined, và giá trị cũ có, cân nhắc phục hồi
            if ((newValue === "" || newValue === undefined) && oldValue !== undefined && !(oldValue === "" || oldValue === undefined) ) {
                let restore = true;
                // Không phục hồi nếu editor không cho phép xóa (ví dụ: list không có clearable:false)
                if (editorParams && editorParams.clearable === false) {
                    restore = false;
                }

                if (restore) {
                    isRestoringCell = true;
                    cell.setValue(oldValue, true); // true để không trigger cellEdited nữa
                    console.log(`Cell ${key} restored to '${oldValue}' as new value '${newValue}' was empty/undefined.`);
                    delete oldValues[key];
                    return;
                } else {
                    console.log(`Cell ${key} not restored. New: '${newValue}', Old: '${oldValue}'. Clearable: ${editorParams ? editorParams.clearable : 'N/A'}`);
                }
            }
        
            // Nếu thực sự có thay đổi, đánh dấu đã edit và cập nhật actions
            if (typeof editedRows !== "undefined" && editedRows.add) {
                editedRows.add(key);
            }
            if (typeof updateActionsColumn === "function") {
                updateActionsColumn(row);
            }

            // Hiển thị nút Save cho dữ liệu từ CSDL
            if (!window.isDataFromUpload && !window.isDataFromXML) {
                const rowData = row.getData();
                const allRows = cell.getTable().getRows();
                const arrayIndex = allRows.indexOf(row);

                console.log(`Cell edited - Row array index: ${arrayIndex}, ID: ${rowData.id}, Field: ${field}`);

                if (rowData.id) {
                    // Tìm nút Save trong action column của row này
                    const saveBtn = row.getElement().querySelector('.save-row-btn');
                    if (saveBtn) {
                        saveBtn.style.display = 'inline-block';
                        console.log(`Save button shown for row array index ${arrayIndex}, ID ${rowData.id}`);
                    } else {
                        console.warn(`Save button not found for row array index ${arrayIndex}, ID ${rowData.id}`);
                        // Thử tìm lại sau một chút
                        setTimeout(() => {
                            const saveBtnRetry = row.getElement().querySelector('.save-row-btn');
                            if (saveBtnRetry) {
                                saveBtnRetry.style.display = 'inline-block';
                                console.log(`Save button found on retry for row ${arrayIndex}`);
                            }
                        }, 100);
                    }
                }
            }

            console.log(`Cell ${key} (Old: '${oldValue}', New: '${newValue}') has been marked as edited.`);
            delete oldValues[key];

            if (onEditedCallback) {
                onEditedCallback(cell); // Gọi callback sau khi logic chung hoàn tất
            }
        },
        
        cellEditCancelled: function(cell) {
            const key = getCellKey(cell);
            // Khi cancel, chỉ cần xóa giá trị cũ đã lưu
            if (oldValues[key] !== undefined) {
                console.log(`Cell ${key} edit cancelled. Old value '${oldValues[key]}' discarded.`);
                delete oldValues[key];
            }
        },
    };
}

// Hàm helper để tạo column với events
function createEditableColumn(title, field, editor = "input", customOptions = {}, headerFilterConfig = true, onCellEditedUserCallback = null) {
    const eventHandlers = createCellEventHandlers(onCellEditedUserCallback);
    let defaultHeaderFilterOptions = {};
    if (headerFilterConfig === true) {
        defaultHeaderFilterOptions = { headerFilter: true };
    } else if (typeof headerFilterConfig === 'string') {
        defaultHeaderFilterOptions = { headerFilter: headerFilterConfig }; // e.g., headerFilter: "list"
    } else if (typeof headerFilterConfig === 'object' && headerFilterConfig !== null) {
        // If headerFilterConfig is an object, it should contain the headerFilter type
        // and potentially headerFilterFunc, headerFilterParams, etc.
        // Example: { headerFilter: "list", headerFilterFunc: myFunc, headerFilterParams: {...} }
        // Ensure headerFilter is explicitly set if not present in the object
        if (headerFilterConfig.headerFilter === undefined) {
             headerFilterConfig.headerFilter = true; // Assume true if object provided but type not specified
        }
        defaultHeaderFilterOptions = headerFilterConfig; // Use the object directly
    } else {
         defaultHeaderFilterOptions = { headerFilter: false };
    }
    return {
        title: title,
        field: field,
        editor: editor,
        // headerFilter: headerFilter,
        headerVertical: false,
        // ...createCellEventHandlers(), // Áp dụng events chung
        ...eventHandlers, // Áp dụng events đã bao gồm callback
        ...customOptions, // Cho phép override hoặc thêm options tùy chỉnh
        ...defaultHeaderFilterOptions // Apply header filter options
    };
}

// ===== CẬP NHẬT CỘT ACTIONS =====
function updateActionsColumn(row) {
    // Sử dụng position thay vì index để tránh lỗi với maLK
    const rowPosition = row.getPosition();

    // Lấy element của dòng
    const rowEl = row.getElement();
    if (!rowEl) return;

    // Tìm nút save trong dòng này
    const saveButton = $(rowEl).find('.save-row-btn');

    if (editedRows.has(rowPosition)) {
        // Row đã được edit, hiển thị button Save
        saveButton.show(); // hoặc saveButton.css('display', 'inline-block');
    } else {
        // Row chưa edit, ẩn button Save
        saveButton.hide();
    }
}

// Hàm lấy dữ liệu danh mục đã load dưới dạng object {value: label}
// Có thể lọc theo trường cha và giá trị cha
function getFilteredCategoryValuesMap(category, parentField = null, parentValue = null) {
    const data = getCategoryDataArray(category); // Get raw array data
    const values = {};

    data.forEach(item => {
        // Check if the item has a value and it's not empty
        if (item.value !== "" && item.value !== null && item.value !== undefined) {
             // If parentField and parentValue are provided, filter by parent
            if (parentField && parentValue) {
                if (item[parentField] === parentValue) {
                    values[item.value] = item.label;
                }
            } else {
                // If no parent filter, include all items
                values[item.value] = item.label;
            }
        }
    });
    return values;
}

// Custom dynamic list editor
const dynamicListEditor = function(cell, onRendered, success, cancel, editorParams){
    // BEGIN DEBUGGING LINES
    console.log("dynamicListEditor called. Cell:", cell.getField());
    console.log("Tabulator object:", Tabulator);
    if (Tabulator) {
        console.log("Tabulator.editors object:", Tabulator.editors);
        if (Tabulator.editors) {
            console.log("Tabulator.editors.list function:", Tabulator.editors.list);
            console.log("Type of Tabulator.editors.list:", typeof Tabulator.editors.list);
        } else {
            console.error("Tabulator.editors IS UNDEFINED!");
        }
    } else {
        console.error("Tabulator IS UNDEFINED!");
    }
    // END DEBUGGING LINES

    const row = cell.getRow();
    const parentField = editorParams.parentField; 
    const filterCategory = editorParams.filterCategory; 

    const parentValue = parentField ? row.getData()[parentField] : null;
    
    const filteredValues = getFilteredCategoryValuesMap(filterCategory, parentField, parentValue);

    const listEditorParams = {
        values: filteredValues,
        placeholderEmpty: editorParams.placeholderEmpty || "Không có kết quả",
        autocomplete: editorParams.autocomplete || true,
        listOnEmpty: editorParams.listOnEmpty || true,
        filterDelay: editorParams.filterDelay || 100,
        clearable: editorParams.clearable !== undefined ? editorParams.clearable : true,
    };

    const editor = Tabulator.editors.list(cell, onRendered, success, cancel, listEditorParams);
    return editor;
};

// Helper to create a non-editable column (e.g., for Date/DateTime formatters)
function createColumn(title, field, customOptions = {}, headerFilterConfig = true) {
     return createEditableColumn(title, field, false, customOptions, headerFilterConfig, null); // Use false for editor
};

// Hàm lấy dữ liệu danh mục đã load dưới dạng mảng
function getCategoryDataArray(category) {
    return categoryData[category] || [];
}

// Hàm cập nhật địa chỉ cho một hàng, sử dụng cho XML1
function updateDiaChiForRow(row) {
    if (!row) return;
    const rowData = row.getData();

    const maTinh = rowData.maTinhCuTru;
    const maHuyen = rowData.maHuyenCuTru;
    const maXa = rowData.maXaCuTru;

    const tinhValues = getCategoryValues("tinh"); // { "MA": "MA - TEN_TINH", ... }
    const huyenValues = getCategoryValues("quanhuyen");
    const xaValues = getCategoryValues("xaphuong");

    const getLabelPart = (code, categoryMap) => {
        if (!code || !categoryMap || !categoryMap[code]) {
            return "";
        }
        const fullLabel = categoryMap[code]; // Ví dụ: "01 - Hà Nội"
        // Chỉ lấy phần tên sau dấu " - "
        return fullLabel.includes(' - ') ? fullLabel.split(' - ').slice(1).join(' - ').trim() : fullLabel.trim();
    };

    const tenXa = getLabelPart(maXa, xaValues);
    const tenHuyen = getLabelPart(maHuyen, huyenValues);
    const tenTinh = getLabelPart(maTinh, tinhValues);

    const diaChiParts = [tenXa, tenHuyen, tenTinh].filter(part => part && part.length > 0);
    const newDiaChi = diaChiParts.join(", ");

    if (newDiaChi !== rowData.diaChi) {
        row.update({ diaChi: newDiaChi });
    }
}

// Add header filter functions for cascading filters
function filterHuyenByTinh(headerValue, rowValue, rowData, filterParams) {
    // headerValue: Giá trị được chọn trong bộ lọc header của cột 'maHuyenCuTru'
    // rowValue: Giá trị của ô 'maHuyenCuTru' trong dòng hiện tại
    // rowData: Toàn bộ dữ liệu của dòng hiện tại
    // filterParams: Tham số tùy chỉnh (không dùng ở đây)

    const table = this; // 'this' là instance Tabulator
    // Lấy giá trị được chọn trong bộ lọc header của cột 'maTinhCuTru'
    const tinhFilter = table.getHeaderFilters().find(f => f.field === 'maTinhCuTru');
    const selectedTinh = tinhFilter ? tinhFilter.value : null;

    // Nếu không có tỉnh nào được chọn, hiển thị tất cả các huyện có giá trị
    if (!selectedTinh) {
        return rowValue !== undefined && rowValue !== null && rowValue !== "";
    }

    // Nếu giá trị huyện của dòng hiện tại rỗng, ẩn nó khi có tỉnh được chọn
    if (!rowValue) {
        return false;
    }

    // Kiểm tra xem huyện của dòng hiện tại có thuộc tỉnh đã chọn hay không
    const huyenData = getCategoryDataArray('quanhuyen');
    console.log('Huyen Data:', huyenData);
    const district = huyenData.find(item => item.value === rowValue);

    // Trả về true nếu tìm thấy huyện và tinh_id của nó khớp với tỉnh đã chọn
    return district && district.tinh_id === selectedTinh;
}

function filterXaByHuyen(headerValue, rowValue, rowData, filterParams) {
    const table = this; // 'this' là instance Tabulator
    const huyenFilter = table.getHeaderFilters().find(f => f.field === 'maHuyenCuTru');
    const selectedHuyen = huyenFilter ? huyenFilter.value : null;

    // Logic tương tự như filterHuyenByTinh, nhưng dựa vào huyen_id và selectedHuyen
    if (!selectedHuyen) return rowValue !== undefined && rowValue !== null && rowValue !== "";
    if (!rowValue) return false;
    const xaData = getCategoryDataArray('xaphuong');
    const ward = xaData.find(item => item.value === rowValue);
    return ward && ward.huyen_id === selectedHuyen;
}

// Custom editor for numbers with thousand separators during input
const thousandSeparatorEditor = function(cell, onRendered, success, cancel, editorParams) {
    const editor = document.createElement("input");
    editor.setAttribute("type", "text");
    editor.style.padding = "4px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";
    editor.style.textAlign = "right";

    const initialValue = cell.getValue();
    const precision = editorParams && editorParams.precision !== undefined ? editorParams.precision : 2;
    const decimalChar = editorParams && editorParams.decimalSeparator ? editorParams.decimalSeparator : ".";
    const thousandChar = editorParams && editorParams.thousandSeparator ? editorParams.thousandSeparator : ",";
    const allowNegative = editorParams && editorParams.allowNegative !== undefined ? editorParams.allowNegative : true;

    // Helper to format number for display in input
    function formatForInput(inputValue) {
        if (inputValue === null || inputValue === undefined || inputValue === "") return "";
        let numStr = String(inputValue);

        let sign = "";
        if (allowNegative && numStr.startsWith("-")) {
            sign = "-";
            numStr = numStr.substring(1);
        }

        // Remove all non-digit except decimalChar
        let cleanedStr = numStr.replace(new RegExp(`[^0-9\\${decimalChar}]`, "g"), "");
        let decimalPointFound = numStr.includes(decimalChar);

        let parts = cleanedStr.split(decimalChar);
        let integerPart = parts[0];
        let fractionalPart = parts.length > 1 ? parts[1] : undefined;

        integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandChar);

        let result = sign + integerPart;
        if (fractionalPart !== undefined) {
            result += decimalChar + fractionalPart.substring(0, precision);
        } else if (decimalPointFound && numStr.endsWith(decimalChar)) {
            result += decimalChar;
        }
        return result;
    }

    // Helper to unformat (get raw number for saving)
    function unformatForSave(value) {
        if (value === null || value === undefined || value === "") return null;
        let numStr = String(value).replace(new RegExp(`\\${thousandChar}`, 'g'), '');
        numStr = numStr.replace(decimalChar, '.');
        if (allowNegative && numStr.startsWith('-')) {
            // ok
        } else {
            numStr = numStr.replace(/[^0-9.]/g, '');
        }
        const num = parseFloat(numStr);
        return isNaN(num) ? null : parseFloat(num.toFixed(precision));
    }

    editor.value = formatForInput(initialValue);

    onRendered(() => {
        editor.focus();
        editor.style.height = "100%";
        if (editorParams && editorParams.selectContents) {
            editor.select();
        }
    });

    function successFunc() {
        const unformattedValue = unformatForSave(editor.value);
        success(unformattedValue);
    }

    editor.addEventListener("blur", successFunc);

    editor.addEventListener("keydown", function(e) {
        if (e.key === "Enter") {
            e.preventDefault();
            successFunc();
        }
        if (e.key === "Escape") {
            e.preventDefault();
            cancel();
        }

        const allowedKeys = [
            "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown",
            "Backspace", "Delete", "Tab", "Home", "End"
        ];
        if (allowedKeys.includes(e.key) || (e.ctrlKey || e.metaKey)) {
            return;
        }
        if (e.key === decimalChar && editor.value.indexOf(decimalChar) === -1) {
            return;
        }
        if (allowNegative && e.key === "-" && editor.value.indexOf("-") === -1 && editor.selectionStart === 0) {
            return;
        }
        if (!/^[0-9]$/.test(e.key)) {
            e.preventDefault();
        }
    });

    editor.addEventListener("input", function(e) {
        const originalValue = editor.value;
        const originalCursorPos = editor.selectionStart;

        const formattedValue = formatForInput(originalValue);

        if (editor.value !== formattedValue) {
            editor.value = formattedValue;
            const diff = formattedValue.length - originalValue.length;
            let newCursorPos = originalCursorPos + diff;
            newCursorPos = Math.max(0, Math.min(newCursorPos, formattedValue.length));
            editor.setSelectionRange(newCursorPos, newCursorPos);
        }
    });

    // Handle paste event: only allow valid numbers
    editor.addEventListener("paste", function(e) {
        e.preventDefault();
        let pasteData = (e.clipboardData || window.clipboardData).getData('text');
        // Remove all except digits, decimalChar, negative sign (if allowed)
        let regexStr = allowNegative ? `[^0-9\\${decimalChar}-]` : `[^0-9\\${decimalChar}]`;
        pasteData = pasteData.replace(new RegExp(regexStr, "g"), "");
        // Only allow one decimalChar and one negative sign at the start
        let parts = pasteData.split(decimalChar);
        let integerPart = parts[0].replace(/-/g, "");
        let fractionalPart = parts[1] ? parts[1].replace(/-/g, "") : "";
        let sign = (allowNegative && pasteData.startsWith("-")) ? "-" : "";
        let cleanPaste = sign + integerPart;
        if (parts.length > 1) {
            cleanPaste += decimalChar + fractionalPart;
        }
        document.execCommand("insertText", false, cleanPaste);
    });

    return editor;
};

/**
 * Convert camelCase to UPPER_SNAKE_CASE
 * @param {string} camelCase - camelCase string
 * @returns {string} UPPER_SNAKE_CASE string
 */
function camelCaseToUpperSnakeCase(camelCase) {
    // Handle special cases first
    const specialCases = {
        'maLK': 'MA_LK',
        'maBN': 'MA_BN',
        'hoTen': 'HO_TEN',
        'soCCCD': 'SO_CCCD',
        'ngaySinh': 'NGAY_SINH',
        'gioiTinh': 'GIOI_TINH',
        'maTheBHYT': 'MA_THE_BHYT',
        'maDKBD': 'MA_DKBD',
        'gtTheTu': 'GT_THE_TU',
        'gtTheDen': 'GT_THE_DEN',
        'maDoiTuongKCB': 'MA_DOITUONG_KCB',
        'ngayVao': 'NGAY_VAO',
        'maLoaiKCB': 'MA_LOAI_KCB',
        'maCSKCB': 'MA_CSKCB',
        'maDichVu': 'MA_DICH_VU',
        'tenDichVu': 'TEN_DICH_VU',
        'maThuoc': 'MA_THUOC',
        'tenThuoc': 'TEN_THUOC',
        'maVatTu': 'MA_VAT_TU',
        'tenVatTu': 'TEN_VAT_TU',
        'ngayYL': 'NGAY_YL',
        'duPhong': 'DU_PHONG'
    };
    
    if (specialCases[camelCase]) {
        return specialCases[camelCase];
    }
    
    // General conversion
    return camelCase.replace(/([A-Z])/g, '_$1').toUpperCase();
}

/**
 * Escape special characters in XML
 * @param {string} str - String to escape
 * @returns {string} Escaped string
 */
function escapeXml(str) {
    return str.replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&apos;');
}

function sumByCondition(data, valueField, conditionField, conditionValue) {
    return data.reduce((sum, row) => {
        if (row[conditionField] == conditionValue) {
            return sum + parseFloat(row[valueField] || 0);
        }
        return sum;
    }, 0);
}

// Debounce utility để tránh tính toán quá nhiều lần
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Helper function để parse số an toàn
function safeParseFloat(value) {
    if (value === null || value === undefined || value === '') {
        return 0;
    }
    // Xử lý chuỗi có dấu phân cách
    if (typeof value === 'string') {
        value = value.replace(/,/g, '');
    }
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
}

// Helper function để kiểm tra bảng có sẵn sàng không
function isTableReady(tableId) {
    const table = document.getElementById(tableId);
    if (!table || !table._tabulator) {
        console.log(`Bảng ${tableId} chưa được khởi tạo`);

        // Thử khởi tạo bảng nếu element tồn tại nhưng chưa có tabulator
        if (table && !table._tabulator) {
            console.log(`Đang thử khởi tạo bảng ${tableId}...`);
            try {
                // Lấy XML type từ table ID
                const xmlType = tableId.replace('-table', '').toUpperCase();

                // Kiểm tra xem có hàm setupTables không
                if (typeof window.TabulatorXMLTables !== 'undefined' &&
                    typeof window.TabulatorXMLTables.setupTables === 'function') {

                    // Thử khởi tạo bảng
                    window.TabulatorXMLTables.setupTables([xmlType]);

                    // Kiểm tra lại sau khi khởi tạo
                    setTimeout(() => {
                        const tableAfterInit = document.getElementById(tableId);
                        if (tableAfterInit && tableAfterInit._tabulator) {
                            console.log(`Đã khởi tạo thành công bảng ${tableId}`);
                        }
                    }, 100);
                }
            } catch (error) {
                console.warn(`Không thể khởi tạo bảng ${tableId}:`, error);
            }
        }

        return false;
    }
    return true;
}

// Helper function để lấy dữ liệu từ bảng
function getTableData(tableId) {
    const table = document.getElementById(tableId);
    if (!table || !table._tabulator) {
        console.warn(`getTableData: ${tableId} - Table not found`);
        return [];
    }

    try {
        // Kiểm tra pagination mode
        const paginationMode = table._tabulator.options.paginationMode;
        console.log(`[${tableId}] Pagination mode:`, paginationMode);

        let data;
        if (paginationMode === 'remote') {
            // Với remote pagination, chỉ lấy dữ liệu trang hiện tại
            data = table._tabulator.getData("active");
            console.log(`[${tableId}] Remote mode - Current page data:`, data.length, 'rows');
        } else {
            // Với local pagination, lấy tất cả dữ liệu đã được lọc
            data = table._tabulator.getData("active");
            console.log(`[${tableId}] Local mode - All filtered data:`, data.length, 'rows');
        }

        return data || [];
    } catch (error) {
        console.error(`Error getting data from ${tableId}:`, error);
        return [];
    }
}

/**
 * Cập nhật thông tin tổng hợp từ dữ liệu hiện tại trong bảng XML1
 * Hiển thị tổng các giá trị ở footer của bảng XML1 và các tab liên quan
 */
function updateXML1Summary(summaryFromAPI = null) {
    console.log("=== updateXML1Summary called ===", summaryFromAPI ? "with API data" : "with table data");

    if (!isTableReady('xml1-table')) {
        return;
    }

    // Kiểm tra nguồn dữ liệu hiện tại
    const isFromUpload = window.isDataFromUpload || window.isDataFromXML || false;
    console.log("Data source - isFromUpload:", isFromUpload, "summaryFromAPI:", !!summaryFromAPI);

    // Kiểm tra xem có summary data từ API không (database mode) và không phải dữ liệu upload
    if (summaryFromAPI && typeof summaryFromAPI === 'object' && !isFromUpload) {
        console.log("Using summary data from API:", summaryFromAPI);

        const totalRecords = summaryFromAPI.total_records || 0;
        const totalTongChiBV = summaryFromAPI.total_tTongChiBV || 0;
        const totalTongChiBH = summaryFromAPI.total_tTongChiBH || 0;
        const totalBHTT = summaryFromAPI.total_tBHTT || 0;
        const totalBNCCT = summaryFromAPI.total_tBNCCT || 0;
        const totalBNTT = summaryFromAPI.total_tBNTT || 0;
        const totalThuoc = summaryFromAPI.total_tThuoc || 0;
        const totalVTYT = summaryFromAPI.total_tVTYT || 0;

        const dataInfoText = `${totalRecords} bản ghi (từ CSDL)`;

        const summaryHTML = `
            <div style="display: flex; align-items: center; gap: 15px; padding: 8px 12px; background: #f8f9fa; border-top: 1px solid #dee2e6; font-size: 13px;">
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BV:</span>
                    <span style="color: #28a745; font-weight: bold;">${formatNumber(totalTongChiBV)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BH:</span>
                    <span style="color: #28a745; font-weight: bold;">${formatNumber(totalTongChiBH)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                    <span style="color: #17a2b8; font-weight: bold;">${formatNumber(totalBHTT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng BNCCT:</span>
                    <span style="color: #ffc107; font-weight: bold;">${formatNumber(totalBNCCT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng BNTT:</span>
                    <span style="color: #dc3545; font-weight: bold;">${formatNumber(totalBNTT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Thuốc:</span>
                    <span style="color: #007bff; font-weight: bold;">${formatNumber(totalThuoc)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng VTYT:</span>
                    <span style="color: #007bff; font-weight: bold;">${formatNumber(totalVTYT)}</span>
                </div>
                <div style="display: flex; align-items: center; margin-left: auto;">
                    <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
                </div>
            </div>
        `;

        updateFooterForTabs(['xml1', 'xml4', 'xml5', 'xml6', 'xml7', 'xml8', 'xml9', 'xml10', 'xml11', 'xml12', 'xml13', 'xml14', 'xml15'], summaryHTML);
        console.log("=== updateXML1Summary completed with API data ===");
        return;
    }

    // Lấy dữ liệu hiện tại từ bảng (upload mode)
    const data = getTableData('xml1-table');

    if (data.length === 0) {
        console.log("XML1: Không có dữ liệu để tính tổng");
        // Hiển thị tổng = 0, nhưng vẫn hiển thị số dòng
        const summaryHTML = createEmptySummaryHTML('xml1');
        updateFooterForTabs(['xml1', 'xml4', 'xml5', 'xml6', 'xml7', 'xml8', 'xml9', 'xml10', 'xml11', 'xml12', 'xml13', 'xml14', 'xml15'], summaryHTML);
        return;
    }
    // Nếu dữ liệu từ CSDL và pagination là remote, thông báo rằng đây là tổng của trang hiện tại
    const isRemotePagination = !window.isDataFromUpload && document.getElementById('xml1-table')?._tabulator?.options.paginationMode === 'remote';
    const dataInfoText = isRemotePagination ? `Dữ liệu trang hiện tại: ${data.length} dòng` : `Dữ liệu: ${data.length} dòng`;

    console.log(`XML1: Đang tính tổng cho ${data.length} dòng dữ liệu`);

    // Tính tổng các giá trị với xử lý an toàn
    let totalTongChiBV = 0;
    let totalTongChiBH = 0;
    let totalBNTT = 0;
    let totalBNCCT = 0;
    let totalBHTT = 0;
    let totalThuoc = 0;
    let totalVTYT = 0;

    data.forEach(function(row, index) {
        const tTongChiBV = safeParseFloat(row.tTongChiBV);
        const tTongChiBH = safeParseFloat(row.tTongChiBH);
        const tBNTT = safeParseFloat(row.tBNTT);
        const tBNCCT = safeParseFloat(row.tBNCCT);
        const tBHTT = safeParseFloat(row.tBHTT);
        const tThuoc = safeParseFloat(row.tThuoc);
        const tVTYT = safeParseFloat(row.tVTYT);

        totalTongChiBV += tTongChiBV;
        totalTongChiBH += tTongChiBH;
        totalBNTT += tBNTT;
        totalBNCCT += tBNCCT;
        totalBHTT += tBHTT;
        totalThuoc += tThuoc;
        totalVTYT += tVTYT;

        // Debug cho vài dòng đầu
        if (index < 3) {
            console.log(`XML1 Row ${index}:`, {
                tTongChiBV, tTongChiBH, tBNTT, tBNCCT, tBHTT, tThuoc, tVTYT
            });
        }
    });

    console.log("XML1 Totals:", {
        totalTongChiBV, totalTongChiBH, totalBNTT, totalBNCCT, totalBHTT, totalThuoc, totalVTYT
    });

    // Định dạng số với dấu phân cách hàng nghìn
    function formatNumber(num) {
        return new Intl.NumberFormat('vi-VN').format(Math.round(num * 100) / 100);
    }

    // Cập nhật nội dung footer cho XML1 và các tab XML4-XML15
    const summaryHTML = `
        <div class="xml1-summary-container" style="display: flex; flex-wrap: wrap; gap: 15px; padding: 10px; background-color: #f8f9fa; border-top: 2px solid #007bff;">
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng chi BV:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalTongChiBV)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng chi BH:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalTongChiBH)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng BNTT:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalBNTT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng BNCCT:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalBNCCT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalBHTT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Thuốc:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalThuoc)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng VTYT:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalVTYT)}</span>
            </div>
            <div style="display: flex; align-items: center; margin-left: auto;">
                <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
            </div>
        </div>
    `;

    // Cập nhật footer cho XML1 và các tab XML4-XML15
    updateFooterForTabs(['xml1', 'xml4', 'xml5', 'xml6', 'xml7', 'xml8', 'xml9', 'xml10', 'xml11', 'xml12', 'xml13', 'xml14', 'xml15'], summaryHTML);
    
    console.log("=== updateXML1Summary completed ===");
}

/**
 * Cập nhật thông tin tổng hợp từ dữ liệu hiện tại trong bảng XML2
 * Hiển thị tổng các giá trị ở footer của bảng XML2
 */
function updateXML2Summary(summaryFromAPI = null) {
    console.log("=== updateXML2Summary called ===", summaryFromAPI ? "with API data" : "with table data");

    if (!isTableReady('xml2-table')) {
        console.log("XML2 table not ready, scheduling retry...");
        return;
    }

    // Kiểm tra nguồn dữ liệu hiện tại
    const isFromUpload = window.isDataFromUpload || window.isDataFromXML || false;
    console.log("XML2 Data source - isFromUpload:", isFromUpload, "summaryFromAPI:", !!summaryFromAPI);

    // Kiểm tra xem có summary data từ API không (database mode) và không phải dữ liệu upload
    if (summaryFromAPI && typeof summaryFromAPI === 'object' && !isFromUpload) {
        console.log("Using XML2 summary data from API:", summaryFromAPI);

        const totalRecords = summaryFromAPI.total_records || 0;
        const totalThanhTienBV = summaryFromAPI.total_thanhTienBV || 0;
        const totalThanhTienBH = summaryFromAPI.total_thanhTienBH || 0;
        const totalBHTT = summaryFromAPI.total_tBHTT || 0;
        const totalBNCCT = summaryFromAPI.total_tBNCCT || 0;
        const totalBNTT = summaryFromAPI.total_tBNTT || 0;
        const totalThuoc = summaryFromAPI.total_thuoc || 0;
        const totalMau = summaryFromAPI.total_mau || 0;

        const dataInfoText = `${totalRecords} bản ghi (từ CSDL)`;

        // Định dạng số với dấu phân cách hàng nghìn
        function formatNumber(num) {
            return new Intl.NumberFormat('vi-VN').format(Math.round(num * 100) / 100);
        }

        // Tạo HTML summary cho XML2
        const summaryHTML = `
            <div class="xml2-summary-container" style="display: flex; flex-wrap: wrap; gap: 15px; padding: 10px; background-color: #f8f9fa; border-top: 2px solid #007bff;">
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BV:</span>
                    <span style="color: #007bff; font-weight: bold;">${formatNumber(totalThanhTienBV)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BH:</span>
                    <span style="color: #28a745; font-weight: bold;">${formatNumber(totalThanhTienBH)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                    <span style="color: #17a2b8; font-weight: bold;">${formatNumber(totalBHTT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Thuốc:</span>
                    <span style="color: #6f42c1; font-weight: bold;">${formatNumber(totalThuoc)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Máu:</span>
                    <span style="color: #e83e8c; font-weight: bold;">${formatNumber(totalMau)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
                </div>
            </div>
        `;

        updateFooterForTabs(['xml2'], summaryHTML);
        console.log("=== updateXML2Summary completed with API data ===");
        return;
    }

    // Lấy dữ liệu hiện tại từ bảng (upload mode)
    const data = getTableData('xml2-table') || [];

    console.log(`XML2: Đang tính tổng cho ${data.length} dòng dữ liệu`);

    if (!Array.isArray(data) || data.length === 0) {
        console.log("XML2: Không có dữ liệu để tính tổng");
        const summaryHTML = createEmptySummaryHTML('xml2');
        updateFooterForTabs(['xml2'], summaryHTML);
        return;
    }
    const isRemotePagination = !window.isDataFromUpload && document.getElementById('xml2-table')?._tabulator?.options.paginationMode === 'remote';
    const dataInfoText = isRemotePagination ? `Dữ liệu trang hiện tại: ${data.length} dòng` : `Dữ liệu: ${data.length} dòng`;

    console.log(`XML2: Đang tính tổng cho ${data.length} dòng dữ liệu`);

    // Tổng hợp các trường cơ bản
    let totalTongChiBV = 0;
    let totalTongChiBH = 0;
    let totalBNTT = 0;
    let totalBNCCT = 0;
    let totalBHTT = 0;
    let totalThuoc = 0;
    let totalMau = 0;

    data.forEach(function(row, index) {
        const thanhTienBV = safeParseFloat(row.thanhTienBV);
        const thanhTienBH = safeParseFloat(row.thanhTienBH);
        const tBNTT = safeParseFloat(row.tBNTT);
        const tBNCCT = safeParseFloat(row.tBNCCT);
        const tBHTT = safeParseFloat(row.tBHTT);

        totalTongChiBV += thanhTienBV;
        totalTongChiBH += thanhTienBH;
        totalBNTT += tBNTT;
        totalBNCCT += tBNCCT;
        totalBHTT += tBHTT;

        // Tính tổng thuốc (maNhom = 4)
        const maNhom = row.maNhom ? row.maNhom.toString() : '';
        if (maNhom === '4') {
            totalThuoc += tBHTT;
        }

        // Tính tổng máu (maNhom = 7)
        if (maNhom === '7') {
            totalMau += tBHTT;
        }

        // Debug cho vài dòng đầu
        if (index < 3) {
            console.log(`XML2 Row ${index}:`, {
                thanhTienBV, thanhTienBH, tBNTT, tBNCCT, tBHTT, maNhom
            });
        }
    });

    console.log("XML2 Totals:", {
        totalTongChiBV, totalTongChiBH, totalBNTT, totalBNCCT, totalBHTT, totalThuoc, totalMau
    });

    // Định dạng số với dấu phân cách hàng nghìn
    function formatNumber(num) {
        return new Intl.NumberFormat('vi-VN').format(Math.round(num * 100) / 100);
    }

    // Tạo HTML summary cho XML2
    const summaryHTML = `
        <div class="xml2-summary-container" style="display: flex; flex-wrap: wrap; gap: 15px; padding: 10px; background-color: #f8f9fa; border-top: 2px solid #007bff;">
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BV:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalTongChiBV)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BH:</span>
                <span style="color: #28a745; font-weight: bold;">${formatNumber(totalTongChiBH)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                <span style="color: #17a2b8; font-weight: bold;">${formatNumber(totalBHTT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Thuốc:</span>
                <span style="color: #6f42c1; font-weight: bold;">${formatNumber(totalThuoc)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Máu:</span>
                <span style="color: #e83e8c; font-weight: bold;">${formatNumber(totalMau)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
            </div>
        </div>
    `;

    updateFooterForTabs(['xml2'], summaryHTML);
    
    console.log("=== updateXML2Summary completed ===");
}

/**
 * Cập nhật thông tin tổng hợp từ dữ liệu hiện tại trong bảng XML3
 * Hiển thị tổng các giá trị ở footer của bảng XML3
 */
function updateXML3Summary(summaryFromAPI = null) {
    console.log("=== updateXML3Summary called ===", summaryFromAPI ? "with API data" : "with table data");

    if (!isTableReady('xml3-table')) {
        console.log("XML3 table not ready, scheduling retry...");
        // Thử lại sau 500ms
        setTimeout(() => {
            if (isTableReady('xml3-table')) {
                updateXML3Summary(summaryFromAPI);
            } else {
                console.log("XML3 table still not ready after retry");
            }
        }, 500);
        return;
    }

    // Kiểm tra nguồn dữ liệu hiện tại
    const isFromUpload = window.isDataFromUpload || window.isDataFromXML || false;
    console.log("XML3 Data source - isFromUpload:", isFromUpload, "summaryFromAPI:", !!summaryFromAPI);

    // Kiểm tra xem có summary data từ API không (database mode) và không phải dữ liệu upload
    if (summaryFromAPI && typeof summaryFromAPI === 'object' && !isFromUpload) {
        console.log("Using XML3 summary data from API:", summaryFromAPI);

        const totalRecords = summaryFromAPI.total_records || 0;
        const totalThanhTienBV = summaryFromAPI.total_thanhTienBV || 0;
        const totalThanhTienBH = summaryFromAPI.total_thanhTienBH || 0;
        const totalBHTT = summaryFromAPI.total_tBHTT || 0;
        const totalBNCCT = summaryFromAPI.total_tBNCCT || 0;
        const totalBNTT = summaryFromAPI.total_tBNTT || 0;
        const totalDVKT = summaryFromAPI.total_dvkt || 0;
        const totalVTYT = summaryFromAPI.total_vtyt || 0;

        const dataInfoText = `${totalRecords} bản ghi (từ CSDL)`;

        // Định dạng số với dấu phân cách hàng nghìn
        function formatNumber(num) {
            return new Intl.NumberFormat('vi-VN').format(Math.round(num * 100) / 100);
        }

        // Tạo HTML summary cho XML3
        const summaryHTML = `
            <div class="xml3-summary-container" style="display: flex; flex-wrap: wrap; gap: 15px; padding: 10px; background-color: #f8f9fa; border-top: 2px solid #007bff;">
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BV:</span>
                    <span style="color: #007bff; font-weight: bold;">${formatNumber(totalThanhTienBV)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BH:</span>
                    <span style="color: #28a745; font-weight: bold;">${formatNumber(totalThanhTienBH)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                    <span style="color: #17a2b8; font-weight: bold;">${formatNumber(totalBHTT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng DVKT:</span>
                    <span style="color: #fd7e14; font-weight: bold;">${formatNumber(totalDVKT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: bold; margin-right: 5px;">Tổng VTYT:</span>
                    <span style="color: #20c997; font-weight: bold;">${formatNumber(totalVTYT)}</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
                </div>
            </div>
        `;

        updateFooterForTabs(['xml3'], summaryHTML);
        console.log("=== updateXML3Summary completed with API data ===");
        return;
    }

    // Lấy dữ liệu hiện tại từ bảng (upload mode)
    const data = getTableData('xml3-table') || [];

    console.log(`XML3: Đang tính tổng cho ${data.length} dòng dữ liệu`);

    if (!Array.isArray(data) || data.length === 0) {
        console.log("XML3: Không có dữ liệu để tính tổng");
        const summaryHTML = createEmptySummaryHTML('xml3'); // Sẽ hiển thị "Không có dữ liệu..."
        updateFooterForTabs(['xml3'], summaryHTML);
        return;
    }
    const isRemotePagination = !window.isDataFromUpload && document.getElementById('xml3-table')?._tabulator?.options.paginationMode === 'remote';
    const dataInfoText = isRemotePagination ? `Dữ liệu trang hiện tại: ${data.length} dòng` : `Dữ liệu: ${data.length} dòng`;

    console.log(`XML3: Đang tính tổng cho ${data.length} dòng dữ liệu`);

    // Tính tổng các giá trị
    let totalTongChiBV = 0;
    let totalTongChiBH = 0;
    let totalBNTT = 0;
    let totalBNCCT = 0;
    let totalBHTT = 0;
    let totalDVKT = 0;
    let totalVTYT = 0;

    data.forEach(function(row, index) {
        const thanhTienBV = safeParseFloat(row.thanhTienBV);
        const thanhTienBH = safeParseFloat(row.thanhTienBH);
        const tBNTT = safeParseFloat(row.tBNTT);
        const tBNCCT = safeParseFloat(row.tBNCCT);
        const tBHTT = safeParseFloat(row.tBHTT);

        totalTongChiBV += thanhTienBV;
        totalTongChiBH += thanhTienBH;
        totalBNTT += tBNTT;
        totalBNCCT += tBNCCT;
        totalBHTT += tBHTT;

        // Tính tổng DVKT (maNhom = 1, 2, 3) và VTYT (maNhom = 5, 6, 8, 9, 10, 11)
        const maNhom = row.maNhom ? row.maNhom.toString() : '';
        if (['1', '2', '3'].includes(maNhom)) {
            totalDVKT += tBHTT;
        } else if (['5', '6', '8', '9', '10', '11'].includes(maNhom)) {
            totalVTYT += tBHTT;
        }

        // Debug cho vài dòng đầu
        if (index < 3) {
            console.log(`XML3 Row ${index}:`, {
                thanhTienBV, thanhTienBH, tBNTT, tBNCCT, tBHTT, maNhom
            });
        }
    });

    console.log("XML3 Totals:", {
        totalTongChiBV, totalTongChiBH, totalBNTT, totalBNCCT, totalBHTT, totalDVKT, totalVTYT
    });

    // Định dạng số với dấu phân cách hàng nghìn
    function formatNumber(num) {
        return new Intl.NumberFormat('vi-VN').format(Math.round(num * 100) / 100);
    }

    // Tạo HTML summary cho XML3
    const summaryHTML = `
        <div class="xml3-summary-container" style="display: flex; flex-wrap: wrap; gap: 15px; padding: 10px; background-color: #f8f9fa; border-top: 2px solid #007bff;">
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BV:</span>
                <span style="color: #007bff; font-weight: bold;">${formatNumber(totalTongChiBV)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng Chi BH:</span>
                <span style="color: #28a745; font-weight: bold;">${formatNumber(totalTongChiBH)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng BHTT:</span>
                <span style="color: #17a2b8; font-weight: bold;">${formatNumber(totalBHTT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng DVKT:</span>
                <span style="color: #fd7e14; font-weight: bold;">${formatNumber(totalDVKT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-weight: bold; margin-right: 5px;">Tổng VTYT:</span>
                <span style="color: #20c997; font-weight: bold;">${formatNumber(totalVTYT)}</span>
            </div>
            <div style="display: flex; align-items: center;">
                <span style="font-size: 12px; color: #6c757d;">${dataInfoText}</span>
            </div>
        </div>
    `;

    updateFooterForTabs(['xml3'], summaryHTML);
    
    console.log("=== updateXML3Summary completed ===");
}

// Hàm đăng ký các nút tải dữ liệu cho XML2
function registerDataLoadButtonsForXML2() {
    // Tìm các nút tải dữ liệu liên quan đến XML2
    document.querySelectorAll('[data-xml-type="XML2"], .load-xml2-data, .upload-xml2-data').forEach(function(button) {
        button.addEventListener('click', function() {
            console.log("XML2 data load button clicked, scheduling summary update");
            // Đợi một khoảng thời gian ngắn để dữ liệu được tải xong
            setTimeout(function() {
                updateXML2Summary();
            }, 1000); // Đợi 1 giây sau khi nhấn nút
        });
    });
    
    // Đăng ký sự kiện cho form upload
    const uploadForms = document.querySelectorAll('form[enctype="multipart/form-data"]');
    uploadForms.forEach(function(form) {
        form.addEventListener('submit', function() {
            console.log("Form submitted, scheduling summary update");
            // Đợi một khoảng thời gian dài hơn vì upload có thể mất nhiều thời gian
            setTimeout(function() {
                updateXML2Summary();
            }, 3000); // Đợi 3 giây sau khi submit form
        });
    });
}

// Hàm đăng ký các nút tải dữ liệu cho XML3
function registerDataLoadButtonsForXML3() {
    // Tìm các nút tải dữ liệu liên quan đến XML3
    document.querySelectorAll('[data-xml-type="XML3"], .load-xml3-data, .upload-xml3-data').forEach(function(button) {
        button.addEventListener('click', function() {
            console.log("XML3 data load button clicked, scheduling summary update");
            // Đợi một khoảng thời gian ngắn để dữ liệu được tải xong
            setTimeout(function() {
                updateXML3Summary();
            }, 1000); // Đợi 1 giây sau khi nhấn nút
        });
    });
    
    // Đăng ký sự kiện cho form upload
    const uploadForms = document.querySelectorAll('form[enctype="multipart/form-data"]');
    uploadForms.forEach(function(form) {
        form.addEventListener('submit', function() {
            console.log("Form submitted, scheduling summary update");
            // Đợi một khoảng thời gian dài hơn vì upload có thể mất nhiều thời gian
            setTimeout(function() {
                updateXML3Summary();
            }, 3000); // Đợi 3 giây sau khi submit form
        });
    });
}

// Hàm khởi tạo tất cả các event listener - cải thiện
function initializeSummaryEventListeners() {
    // Đợi DOM load xong
    document.addEventListener('DOMContentLoaded', function() {
        // Đăng ký các event cho các bảng
        // registerXML1SummaryEvents();
        
        // Đăng ký các event cho các nút tải dữ liệu
        // registerDataLoadButtonsForXML1();
        // registerDataLoadButtonsForXML2();
        // registerDataLoadButtonsForXML3();
        
        // Đăng ký event chuyển tab
        registerTabSummarySwitcher();
        
        // Cập nhật tổng hợp ban đầu cho tab hiện tại
        setTimeout(function() {
            // Xác định tab hiện tại và cập nhật summary tương ứng
            const activeTab = document.querySelector('.nav-link.active, .tab-pane.active');
            if (activeTab) {
                const tabId = activeTab.id || activeTab.getAttribute('href') || activeTab.getAttribute('data-target');
                if (tabId) {
                    const tabName = tabId.replace('#', '');
                    if (tabName.includes('xml2')) {
                        updateXML2Summary();
                    } else if (tabName.includes('xml3')) {
                        updateXML3Summary();
                    } else {
                        updateXML1Summary();
                    }
                }
            } else {
                // Mặc định cập nhật XML1 summary
                updateXML1Summary();
            }
        }, 500);
        
        console.log("Đã khởi tạo tất cả các event listener cho việc cập nhật tổng hợp");
    });
}

// Helper function để tạo summary HTML rỗng
function createEmptySummaryHTML(xmlType) {
    const colors = {
        'xml1': '#007bff', // Màu cho XML1 và các tab liên quan
        'xml2': '#28a745', 
        'xml3': '#ffc107'
    };
    const color = colors[xmlType] || '#6c757d'; // Màu mặc định
    
    return `
        <div class="${xmlType}-summary-container" style="display: flex; align-items: center; padding: 10px; background-color: #f8f9fa; border-top: 2px solid ${color};">
            <span style="color: ${color}; font-weight: bold;">Không có dữ liệu để hiển thị tổng hợp</span>
        </div>
    `;
}

// Tạo các phiên bản debounced của các hàm summary
const debouncedUpdateXML1Summary = debounce(updateXML1Summary, 300);
const debouncedUpdateXML2Summary = debounce(updateXML2Summary, 300);
const debouncedUpdateXML3Summary = debounce(updateXML3Summary, 300);

// Export các hàm debounced để sử dụng trong events của Tabulator
window.debouncedUpdateXML1Summary = debouncedUpdateXML1Summary;
window.debouncedUpdateXML2Summary = debouncedUpdateXML2Summary;
window.debouncedUpdateXML3Summary = debouncedUpdateXML3Summary;

/**
 * Hàm helper để cập nhật footer cho các tab cụ thể
 * @param {Array} tabIds - Mảng các ID tab cần cập nhật (ví dụ: ['xml1', 'xml2'])
 * @param {string} summaryHTML - HTML content cho footer
 */
function updateFooterForTabs(tabIds, summaryHTML) {
    tabIds.forEach(function(tabId) {
        // Tìm footer trong tab pane cụ thể
        const tabPane = document.getElementById(tabId); // tabId là 'xml1', 'xml2', ...
        if (tabPane) {
            const footerElement = tabPane.querySelector('.tabulator-footer-summary');
            if (footerElement) {
                footerElement.innerHTML = summaryHTML;
            }
        }
    });
}

// Cập nhật hàm populateEditModalWithAllData
function populateEditModalWithAllData(dataMap, maLK) {
    $('#modalEditMaLK').text(maLK);
    
    // Duyệt qua từng loại XML
    for (let i = 0; i <= 15; i++) {
        let xmlTypeKey = 'XML' + i;
        let records = dataMap[xmlTypeKey] || [];

        // Nếu là dạng bảng (nhiều dòng)
        if (['XML2','XML3','XML4','XML5','XML6','XML9','XML15'].includes(xmlTypeKey)) {
            // Sử dụng hàm setupModalTables riêng cho modal
            // Thêm một ID tạm thời duy nhất cho mỗi dòng trong modal để làm index
            const recordsProcessedForModal = records.map((record, idx) => ({
                ...record,
                stt: record.stt || record.STT || (idx + 1), // Ưu tiên STT/stt có sẵn, nếu không thì đánh số từ 1
                _tempModalId: idx // _tempModalId sẽ là 0, 1, 2, ...
            }));
            setupModalTables(xmlTypeKey, recordsProcessedForModal);
        } else { // Xử lý cho các XML dạng form (1 dòng)
            if (xmlTypeKey === 'XML1' && records.length > 0) {
                let record = records[0];
                // Lưu các giá trị địa chỉ ban đầu để set sau khi trigger change
                const initialMaTinh = record.maTinhCuTru;
                const initialMaHuyen = record.maHuyenCuTru;
                const initialMaXa = record.maXaCuTru;

                for (let key in record) {
                    if (record.hasOwnProperty(key)) {
                        let inputId = '#modal_' + xmlTypeKey.toLowerCase() + '_' + key;
                        let valueToSet = record[key] !== null && record[key] !== undefined ? record[key] : "";
    
                        // Áp dụng biến đổi nếu có
                        if (fieldTransformers[xmlTypeKey] && fieldTransformers[xmlTypeKey][key] && fieldTransformers[xmlTypeKey][key].toModal) {
                            valueToSet = fieldTransformers[xmlTypeKey][key].toModal(valueToSet);
                            if (valueToSet === undefined) { // Đảm bảo không gán undefined vào input
                                valueToSet = "";
                            }
                        }
                        // Không set giá trị cho các select địa chỉ ở đây, sẽ set sau khi trigger
                        if (key !== 'maTinhCuTru' && key !== 'maHuyenCuTru' && key !== 'maXaCuTru') {
                            $(inputId).val(valueToSet);
                             // Nếu là select2, trigger change để cập nhật hiển thị
                            if ($(inputId).data('select2')) {
                                $(inputId).trigger('change.select2');
                            }
                        }
                    }
                }

                // Xử lý cascading cho địa chỉ của XML1 với delay để đảm bảo combobox đã được khởi tạo
                const $tinhSelect = $('#modal_xml1_maTinhCuTru');
                const $huyenSelect = $('#modal_xml1_maHuyenCuTru');
                const $xaSelect = $('#modal_xml1_maXaCuTru');
                setTimeout(() => {
                    if (initialMaTinh) {
                        $tinhSelect.val(initialMaTinh);
                        $tinhSelect.trigger('change'); // Kích hoạt để populate huyện

                        // Delay để huyện được populate trước khi set giá trị
                        setTimeout(() => {
                            if (initialMaHuyen) {
                                $huyenSelect.val(initialMaHuyen);
                                $huyenSelect.trigger('change'); // Kích hoạt để populate xã

                                // Delay để xã được populate trước khi set giá trị
                                setTimeout(() => {
                                    if (initialMaXa) {
                                        $xaSelect.val(initialMaXa);
                                    }
                                }, 100);
                            }
                        }, 100);
                    }
                }, 100);
                
                // Trigger change.select2 nếu đang sử dụng Select2
                if ($tinhSelect.data('select2')) $tinhSelect.trigger('change.select2');
                if ($huyenSelect.data('select2')) $huyenSelect.trigger('change.select2');
                if ($xaSelect.data('select2')) $xaSelect.trigger('change.select2');

            } else if (records.length > 0) { // Xử lý cho các XML dạng form khác không phải XML1
                let record = records[0];
                for (let key in record) {
                    if (record.hasOwnProperty(key)) {
                        let inputId = '#modal_' + xmlTypeKey.toLowerCase() + '_' + key;
                        let valueToSet = record[key] !== null && record[key] !== undefined ? record[key] : "";

                        if (fieldTransformers[xmlTypeKey] && fieldTransformers[xmlTypeKey][key] && fieldTransformers[xmlTypeKey][key].toModal) {
                            valueToSet = fieldTransformers[xmlTypeKey][key].toModal(valueToSet);
                            if (valueToSet === undefined) {
                                valueToSet = "";
                            }
                        }
                        $(inputId).val(valueToSet);
                        // Nếu là select2, trigger change
                        if ($(inputId).data('select2')) {
                            $(inputId).trigger('change.select2');
                        }
                    }
                }
            }
        }
    }
}

/**
 * Sửa lỗi lấy dữ liệu từ Tabulator khi mở modal chỉnh sửa
 * Đảm bảo dùng đúng biến tableElement._tabulator thay vì tabulatorInstance
 */
async function openEditPreviewModal(maLK, ngayTao) {
    let dataForModal = { maLK: maLK, ngayTao: ngayTao, dataMap: {} };
    let foundDataForModal = false;

    if (window.isDataFromUpload) {
        // TH1: Dữ liệu từ importXML - Lấy từ Tabulator trên trang list
        for (let i = 0; i <= 15; i++) {
            let xmlTypeKey = 'XML' + i;
            let tableId = xmlTypeKey.toLowerCase() + '-table';
            let tableElement = document.getElementById(tableId);
            if (tableElement && tableElement._tabulator) {
                let allDataInTable = tableElement._tabulator.getData();
                let recordsForMaLK = allDataInTable.filter(row => row.maLK === maLK);

                if (recordsForMaLK.length > 0) {
                    dataForModal.dataMap[xmlTypeKey] = recordsForMaLK;
                    foundDataForModal = true;
                }
            }
        }

        // Nếu không tìm thấy dữ liệu, có thể maLK đã thay đổi trong preview
        if (!foundDataForModal) {
            // Lấy tất cả maLK có sẵn để debug
            let availableMaLKs = new Set();
            for (let i = 0; i <= 15; i++) {
                let xmlTypeKey = 'XML' + i;
                let tableId = xmlTypeKey.toLowerCase() + '-table';
                let tableElement = document.getElementById(tableId);
                if (tableElement && tableElement._tabulator) {
                    let allDataInTable = tableElement._tabulator.getData();
                    allDataInTable.forEach(row => {
                        if (row.maLK) availableMaLKs.add(row.maLK);
                    });
                }
            }
            console.log('Available maLKs in preview data:', Array.from(availableMaLKs));
        }
    } else {
        // TH2: Dữ liệu từ CSDL - Gọi AJAX để lấy dữ liệu
        Swal.fire({
            title: 'Đang tải dữ liệu hồ sơ...',
            allowOutsideClick: false,
            didOpen: () => { Swal.showLoading(); }
        });
        
        try {
            const response = await $.ajax({
                url: `/xml4750/api/hoso-by-malk/${maLK}/${ngayTao}/`,
                type: 'GET',
                dataType: 'json'
            });
            
            Swal.close();
            if (response.success && response.data) {
                dataForModal.dataMap = response.data;
                foundDataForModal = Object.keys(dataForModal.dataMap).length > 0;
            } else {
                console.error('openEditPreviewModal: Server response unsuccessful:', response);
                Swal.fire('Lỗi', response.message || 'Không thể tải dữ liệu hồ sơ từ máy chủ.', 'error');
                return;
            }
        } catch (error) {
            Swal.close();
            console.error("openEditPreviewModal: AJAX error:", error);
            
            // Log more details about the error
            if (error.responseJSON) {
                console.error("openEditPreviewModal: Server error response:", error.responseJSON);
            }
            if (error.status) {
                console.error("openEditPreviewModal: HTTP status:", error.status);
            }
            
            Swal.fire('Lỗi', 'Lỗi kết nối khi tải dữ liệu hồ sơ.', 'error');
            return;
        }
    }

    // Kiểm tra xem có tìm thấy dữ liệu không
    if (!foundDataForModal) {
        console.error(`openEditPreviewModal: No data found for maLK ${maLK}`);
        
        if (window.isDataFromUpload) {
            Swal.fire('Không tìm thấy dữ liệu', `Không tìm thấy dữ liệu nào cho Mã Liên Kết ${maLK} trong dữ liệu preview.`, 'warning');
        } else {
            Swal.fire('Không tìm thấy dữ liệu', `Không tìm thấy dữ liệu nào cho Mã Liên Kết ${maLK} và Ngày Tạo ${ngayTao} trong cơ sở dữ liệu.`, 'warning');
        }
        return;
    }
    // Cập nhật thông tin chung trên modal (ví dụ từ XML1 nếu có)
    const xml1RecordForDisplay = dataForModal.dataMap.XML1 && dataForModal.dataMap.XML1.length > 0 ? dataForModal.dataMap.XML1[0] : null;
    if (xml1RecordForDisplay) {
        const formatDisplayNumber = (value) => (value !== null && value !== undefined ? parseFloat(value) : 0).toLocaleString('vi-VN');
        const getNumericValue = (value) => (value !== null && value !== undefined ? parseFloat(value) : 0);

        $('#modalEditHoTen').text(xml1RecordForDisplay.hoTen || '');
        let ngaySinhDisplay = '';
        if (xml1RecordForDisplay.ngaySinh) {
            if (/^\d{8}$/.test(xml1RecordForDisplay.ngaySinh)) { // yyyyMMdd
                ngaySinhDisplay = `${xml1RecordForDisplay.ngaySinh.substring(6,8)}/${xml1RecordForDisplay.ngaySinh.substring(4,6)}/${xml1RecordForDisplay.ngaySinh.substring(0,4)}`;
            } else if (/^\d{12}$/.test(xml1RecordForDisplay.ngaySinh)) { // yyyyMMddHHmm
                 ngaySinhDisplay = `${xml1RecordForDisplay.ngaySinh.substring(6,8)}/${xml1RecordForDisplay.ngaySinh.substring(4,6)}/${xml1RecordForDisplay.ngaySinh.substring(0,4)}`;
            } else {
                ngaySinhDisplay = xml1RecordForDisplay.ngaySinh;
            }
        }
        $('#modalEditNgaySinh').text(ngaySinhDisplay);
        $('#modalEditTongTienThuoc').text(formatDisplayNumber(xml1RecordForDisplay.tThuoc));
        $('#modal_xml1_tThuoc').val(getNumericValue(xml1RecordForDisplay.tThuoc));
        $('#modalEditTongTienVTYT').text(formatDisplayNumber(xml1RecordForDisplay.tVTYT));
        $('#modal_xml1_tVTYT').val(getNumericValue(xml1RecordForDisplay.tVTYT));
        $('#modalEditTongChiBV').text(formatDisplayNumber(xml1RecordForDisplay.tTongChiBV));
        $('#modal_xml1_tTongChiBV').val(getNumericValue(xml1RecordForDisplay.tTongChiBV));
        $('#modalEditTongChiBH').text(formatDisplayNumber(xml1RecordForDisplay.tTongChiBH));
        $('#modal_xml1_tTongChiBH').val(getNumericValue(xml1RecordForDisplay.tTongChiBH));
        $('#modalEditTongBHTT').text(formatDisplayNumber(xml1RecordForDisplay.tBHTT));
        $('#modal_xml1_tBHTT').val(getNumericValue(xml1RecordForDisplay.tBHTT));
        $('#modalEditTongBNCCT').text(formatDisplayNumber(xml1RecordForDisplay.tBNCCT));
        $('#modal_xml1_tBNCCT').val(getNumericValue(xml1RecordForDisplay.tBNCCT));
        $('#modalEditTongBNTT').text(formatDisplayNumber(xml1RecordForDisplay.tBNTT));
        $('#modal_xml1_tBNTT').val(getNumericValue(xml1RecordForDisplay.tBNTT));
        // Cập nhật cho tNguonKhac và tBHTTGDV nếu có
        $('#modal_xml1_tNguonKhac').val(getNumericValue(xml1RecordForDisplay.tNguonKhac));
        $('#modal_xml1_tBHTTGDV').val(getNumericValue(xml1RecordForDisplay.tBHTTGDV));
    } else {
        // Clear nếu không có XML1
        $('#modalEditHoTen, #modalEditNgaySinh, #modalEditTongTienThuoc, #modalEditTongTienVTYT, #modalEditTongChiBV, #modalEditTongChiBH, #modalEditTongBHTT, #modalEditTongBNCCT, #modalEditTongBNTT').text('');
    }

    // Khởi tạo combobox TRƯỚC khi populate dữ liệu
    if (typeof initializeConfiguredModalSelects === 'function') {
        initializeConfiguredModalSelects();
    }

    // Populate modal với dữ liệu đã lấy được
    populateEditModalWithAllData(dataForModal.dataMap, maLK);

    // Set modal data và hiển thị
    $('#editPreviewModal').data('tabulatorRowMaLK', maLK);
    $('#editPreviewModal').data('ngayTao', ngayTao);
    $('#editPreviewModal').data('isDataFromUpload', window.isDataFromUpload);
    $('#editPreviewModal').modal('show');
}
// Hàm để clear modal data khi đóng
function clearModalData() {
    // Clear form fields
    $('#editPreviewForm')[0].reset();
    
    // Clear tabulator data
    for (let i = 0; i <= 15; i++) {
        let xmlTypeKey = 'XML' + i;
        if (['XML2','XML3','XML4','XML5','XML6','XML9','XML15'].includes(xmlTypeKey)) {
            let modalTableId = 'modal_' + xmlTypeKey.toLowerCase() + '-edit-table';
            let modalTableElement = document.getElementById(modalTableId);
            if (modalTableElement && modalTableElement._tabulator) {
                modalTableElement._tabulator.clearData();
            }
        }
    }
}

// Hàm helper để quản lý hiển thị tab trong modal
function updateModalTabVisibility(dataMap) {
    for (let i = 0; i <= 15; i++) {
        let xmlTypeKey = 'XML' + i;
        let records = dataMap[xmlTypeKey] || [];
        let tabId = '#modal-edit-' + xmlTypeKey.toLowerCase() + '-tab';
        let paneId = '#modal-edit-' + xmlTypeKey.toLowerCase();
        
        if (records.length > 0) {
            $(tabId).show();
        } else {
            $(tabId).hide();
            $(paneId).removeClass('show active');
        }
    }
    
    // Kích hoạt tab đầu tiên có dữ liệu
    let firstVisibleTab = $('#modalEditXmlTabs .nav-link:visible:first');
    if (firstVisibleTab.length > 0 && $('#modalEditXmlTabs .nav-link.active:visible').length === 0) {
        firstVisibleTab.tab('show');
    }
}

// Hàm để đăng ký các event listener cho việc cập nhật tổng hợp
function registerXML1SummaryEvents() {
    const xml1Table = document.getElementById('xml1-table');
    if (!xml1Table || !xml1Table._tabulator) {
        return;
    }
    
    // Đăng ký các sự kiện cần thiết để cập nhật tổng hợp
    xml1Table._tabulator.on("dataFiltered", function() {
        updateXML1Summary();
    });
    
    xml1Table._tabulator.on("dataChanged", function() {
        updateXML1Summary();
    });
    
    xml1Table._tabulator.on("dataSorted", function() {
        updateXML1Summary();
    });
    
    xml1Table._tabulator.on("pageLoaded", function() {
        updateXML1Summary();
    });
}

// Hàm đăng ký các event listener cho XML2
function registerXML2SummaryEvents() {
    const xml2Table = document.getElementById('xml2-table');
    if (!xml2Table || !xml2Table._tabulator) {
        console.log("Không thể đăng ký event cho bảng XML2, bảng chưa được khởi tạo");
        return;
    }
    
    // Đăng ký các sự kiện cần thiết để cập nhật tổng hợp
    xml2Table._tabulator.on("dataFiltered", function() {
        updateXML2Summary();
    });
    
    xml2Table._tabulator.on("dataChanged", function() {
        updateXML2Summary();
    });
    
    xml2Table._tabulator.on("dataSorted", function() {
        updateXML2Summary();
    });
    
    xml2Table._tabulator.on("pageLoaded", function() {
        updateXML2Summary();
    });
}

// Hàm để đăng ký các event listener cho XML3
function registerXML3SummaryEvents() {
    const xml3Table = document.getElementById('xml3-table');
    if (!xml3Table || !xml3Table._tabulator) {
        console.log("Không thể đăng ký event cho bảng XML3, bảng chưa được khởi tạo");
        return;
    }
    
    // Đăng ký các sự kiện cần thiết để cập nhật tổng hợp
    xml3Table._tabulator.on("dataFiltered", function() {
        updateXML3Summary();
    });
    
    xml3Table._tabulator.on("dataChanged", function() {
        updateXML3Summary();
    });
    
    xml3Table._tabulator.on("dataSorted", function() {
        updateXML3Summary();
    });
    
    xml3Table._tabulator.on("pageLoaded", function() {
        updateXML3Summary();
    });
}

/**
 * Cải thiện hàm đăng ký chuyển tab để hiển thị đúng summary
 */
function registerTabSummarySwitcher() {
    // Lắng nghe sự kiện chuyển tab (Bootstrap 4/5)
    document.querySelectorAll('a[data-toggle="tab"], button[data-bs-toggle="tab"], a[data-bs-toggle="tab"]').forEach(function(tab) {
        tab.addEventListener('shown.bs.tab', function(e) {
            const targetId = e.target.getAttribute('href') || e.target.getAttribute('data-target') || e.target.getAttribute('data-bs-target');
            if (!targetId) return;
            
            // Lấy tab name từ targetId (bỏ dấu #)
            const tabName = targetId.replace('#', '');
            
            console.log("Tab switched to:", tabName);
            
            // Xác định loại summary cần hiển thị và gọi hàm cập nhật
            // Sử dụng setTimeout để đảm bảo Tabulator đã redraw xong
            setTimeout(() => {
                if (typeof updateXML1Summary === 'function' && typeof updateXML2Summary === 'function' && typeof updateXML3Summary === 'function') {
                    // Check for XML2 tab variations
                    if (tabName.includes("xml2") || tabName === "xml2-tab" || targetId === "#xml2") {
                        console.log("Updating XML2 summary for tab:", tabName);
                        // Kiểm tra xem table có tồn tại không trước khi cập nhật
                        const xml2Table = document.getElementById('xml2-table');
                        if (xml2Table && xml2Table._tabulator) {
                            updateXML2Summary();
                        } else {
                            console.log("XML2 table not initialized yet, skipping summary update");
                        }
                    }
                    // Check for XML3 tab variations
                    else if (tabName.includes("xml3") || tabName === "xml3-tab" || targetId === "#xml3") {
                        console.log("Updating XML3 summary for tab:", tabName);
                        // Kiểm tra xem table có tồn tại không trước khi cập nhật
                        const xml3Table = document.getElementById('xml3-table');
                        if (xml3Table && xml3Table._tabulator) {
                            updateXML3Summary();
                        } else {
                            console.log("XML3 table not initialized yet, skipping summary update");
                        }
                    }
                    // XML1 và các tab khác
                    else {
                        console.log("Updating XML1 summary for tab:", tabName);
                        // XML1 thường đã được khởi tạo
                        const xml1Table = document.getElementById('xml1-table');
                        if (xml1Table && xml1Table._tabulator) {
                            updateXML1Summary();
                        } else {
                            console.log("XML1 table not initialized yet, skipping summary update");
                        }
                    }
                }
            }, 200); // Tăng thời gian chờ để đảm bảo Tabulator đã render xong
        });
    });
 }

// ===== QUẢN LÝ TRẠNG THÁI DỮ LIỆU =====

/**
 * Enum cho các trạng thái dữ liệu
 */
const DATA_SOURCE_TYPE = {
    IMPORTED_XML: 'imported_xml',    // Dữ liệu từ import XML
    DATABASE: 'database'             // Dữ liệu từ cơ sở dữ liệu
};

/**
 * Quản lý trạng thái hiện tại của dữ liệu
 */
window.currentDataSource = DATA_SOURCE_TYPE.DATABASE; // Mặc định là database

/**
 * Cập nhật trạng thái nguồn dữ liệu và UI tương ứng
 */
function updateDataSourceState(sourceType) {
    window.currentDataSource = sourceType;
    window.isDataFromUpload = (sourceType === DATA_SOURCE_TYPE.IMPORTED_XML);
    window.isDataFromXML = (sourceType === DATA_SOURCE_TYPE.IMPORTED_XML);

    // Cập nhật UI buttons
    updateUIForDataSource(sourceType);

    // Trigger event để các component khác có thể lắng nghe
    $(document).trigger('dataSourceChanged', { sourceType: sourceType });
}

/**
 * Cập nhật UI buttons dựa trên nguồn dữ liệu
 */
function updateUIForDataSource(sourceType) {
    const $saveBtn = $('#savePreviewDataBtn');
    const $exportBtn = $('#exportMainXmlBtn');
    const $deleteAllBtn = $('.delete-all-btn');
    const $deleteSelectedBtn = $('.delete-selected-btn');

    if (sourceType === DATA_SOURCE_TYPE.IMPORTED_XML) {
        // Trường hợp 1: Dữ liệu từ import XML
        $saveBtn.show().removeClass('btn-secondary').addClass('btn-primary')
               .attr('title', 'Lưu toàn bộ dữ liệu XML vào cơ sở dữ liệu')
               .find('i').removeClass().addClass('fas fa-save');

        $exportBtn.removeClass('btn-secondary').addClass('btn-info')
                  .attr('title', 'Xuất dữ liệu đã import ra file XML');

        $deleteAllBtn.removeClass('btn-secondary').addClass('btn-danger')
                     .attr('title', 'Xóa tất cả dữ liệu đã import (chỉ trong bộ nhớ)');

        $deleteSelectedBtn.removeClass('btn-secondary').addClass('btn-outline-danger')
                          .attr('title', 'Xóa các bản ghi đã chọn (chỉ trong bộ nhớ)');

        // Hiển thị thông báo trạng thái
        showDataSourceNotification('Đang hiển thị dữ liệu từ file XML đã import', 'info');

    } else {
        // Trường hợp 2: Dữ liệu từ cơ sở dữ liệu
        $saveBtn.hide(); // Ẩn nút lưu vì dữ liệu đã có trong CSDL

        $exportBtn.removeClass('btn-secondary').addClass('btn-info')
                  .attr('title', 'Xuất dữ liệu từ cơ sở dữ liệu ra file XML');

        $deleteAllBtn.removeClass('btn-secondary').addClass('btn-danger')
                     .attr('title', 'Xóa tất cả bản ghi từ cơ sở dữ liệu (không thể hoàn tác)');

        $deleteSelectedBtn.removeClass('btn-secondary').addClass('btn-outline-danger')
                          .attr('title', 'Xóa các bản ghi đã chọn từ cơ sở dữ liệu (không thể hoàn tác)');

        // Hiển thị thông báo trạng thái
        showDataSourceNotification('Đang hiển thị dữ liệu từ cơ sở dữ liệu', 'success');
    }
}

/**
 * Hiển thị thông báo về trạng thái nguồn dữ liệu
 */
function showDataSourceNotification(message, type) {
    // Tạo hoặc cập nhật status indicator
    let $statusIndicator = $('#data-source-status');
    if ($statusIndicator.length === 0) {
        $statusIndicator = $('<div id="data-source-status" class="alert alert-dismissible fade show" style="margin-bottom: 10px;"></div>');
        $('.filter-section').after($statusIndicator);
    }

    // Cập nhật class và nội dung
    $statusIndicator.removeClass('alert-info alert-success alert-warning alert-danger')
                   .addClass(`alert-${type}`)
                   .html(`
                       <i class="fas fa-info-circle"></i>
                       <strong>Trạng thái:</strong> ${message}
                       <button type="button" class="close" data-dismiss="alert">
                           <span>&times;</span>
                       </button>
                   `);

    // Auto hide after 5 seconds
    setTimeout(() => {
        $statusIndicator.fadeOut();
    }, 5000);
}

/**
 * Xử lý lưu dữ liệu - chỉ cho phép khi dữ liệu từ import XML
 */
function handleSaveData() {
    if (window.currentDataSource !== DATA_SOURCE_TYPE.IMPORTED_XML) {
        Swal.fire({
            icon: 'warning',
            title: 'Không thể lưu',
            text: 'Chỉ có thể lưu dữ liệu đã import từ file XML. Dữ liệu từ cơ sở dữ liệu đã được lưu sẵn.'
        });
        return false;
    }

    // Tiếp tục với logic lưu dữ liệu hiện tại
    return true;
}

/**
 * Xử lý xóa dữ liệu với xác nhận khác nhau tùy theo nguồn
 */
function handleDeleteData(deleteType = 'selected') {
    const isFromImport = (window.currentDataSource === DATA_SOURCE_TYPE.IMPORTED_XML);

    let title, text, confirmButtonText;

    if (isFromImport) {
        title = deleteType === 'all' ? 'Xóa tất cả dữ liệu import?' : 'Xóa dữ liệu đã chọn?';
        text = 'Dữ liệu sẽ bị xóa khỏi bộ nhớ (chưa lưu vào CSDL). Bạn có thể import lại nếu cần.';
        confirmButtonText = 'Xóa khỏi bộ nhớ';
    } else {
        title = deleteType === 'all' ? 'Xóa tất cả dữ liệu từ CSDL?' : 'Xóa dữ liệu đã chọn từ CSDL?';
        text = 'CẢNH BÁO: Dữ liệu sẽ bị xóa vĩnh viễn khỏi cơ sở dữ liệu và không thể khôi phục!';
        confirmButtonText = 'Xóa vĩnh viễn';
    }

    return Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: confirmButtonText,
        cancelButtonText: 'Hủy bỏ'
    });
}

// ===== EVENT HANDLERS FOR PREVIEW ACTIONS =====
function deleteSelectedFromPreview() {
    console.log('Deleting selected rows from preview');

    let totalSelected = 0;
    let totalDeleted = 0;

    // Check all tables for selected rows
    for (let i = 0; i <= 15; i++) {
        const xmlType = `XML${i}`;
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (tableElement && tableElement._tabulator) {
            try {
                const selectedRows = tableElement._tabulator.getSelectedRows();
                totalSelected += selectedRows.length;

                if (selectedRows.length > 0) {
                    selectedRows.forEach(row => {
                        row.delete();
                        totalDeleted++;
                    });
                    console.log(`Deleted ${selectedRows.length} rows from ${xmlType}`);
                }
            } catch (error) {
                console.error(`Error deleting selected rows from ${xmlType}:`, error);
            }
        }
    }

    if (totalDeleted > 0) {
        showSuccess(`Đã xóa ${totalDeleted} dòng khỏi xem trước`);
    } else if (totalSelected === 0) {
        showNotification('Vui lòng chọn ít nhất một dòng để xóa', 'warning');
    } else {
        showError('Không thể xóa các dòng đã chọn');
    }
}

function deleteSelectedFromDatabase() {
    console.log('Deleting selected rows from database');

    // Get selected rows from all tables
    let selectedData = [];

    for (let i = 0; i <= 15; i++) {
        const xmlType = `XML${i}`;
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (tableElement && tableElement._tabulator) {
            try {
                const selectedRows = tableElement._tabulator.getSelectedRows();
                selectedRows.forEach(row => {
                    const data = row.getData();
                    if (data.maLK) {
                        selectedData.push({
                            xmlType: xmlType,
                            maLK: data.maLK,
                            rowData: data
                        });
                    }
                });
            } catch (error) {
                console.error(`Error getting selected rows from ${xmlType}:`, error);
            }
        }
    }

    if (selectedData.length === 0) {
        showNotification('Vui lòng chọn ít nhất một dòng để xóa', 'warning');
        return;
    }

    // Group by maLK
    const groupedByMaLK = {};
    selectedData.forEach(item => {
        if (!groupedByMaLK[item.maLK]) {
            groupedByMaLK[item.maLK] = [];
        }
        groupedByMaLK[item.maLK].push(item);
    });

    const maLKList = Object.keys(groupedByMaLK);

    Swal.fire({
        title: 'Xác nhận xóa',
        text: `Bạn có chắc muốn xóa ${maLKList.length} hồ sơ (${selectedData.length} bản ghi) từ cơ sở dữ liệu?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy'
    }).then((result) => {
        if (result.isConfirmed) {
            // Call server to delete
            $.ajax({
                url: '/xml4750/api/delete-selected/',
                type: 'POST',
                data: {
                    'maLK_list': JSON.stringify(maLKList),
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess(`Đã xóa thành công ${response.deleted_count} hồ sơ`);
                        // Reload data
                        loadFromDatabase();
                    } else {
                        showError('Lỗi khi xóa: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Delete error:', error);
                    showError('Lỗi khi xóa dữ liệu: ' + error);
                }
            });
        }
    });
}

// Enhanced date editor that checks format and conditionally allows editing
conditionalDateEditor = function(cell, onRendered, success, cancel, editorParams){
    const value = cell.getValue();
    
    // Check if value contains semicolon (date range format)
    if (value && value.includes(';')) {
        // For date range format, don't allow editing - just cancel immediately
        cancel();
        return;
    }
    
    const editor = document.createElement("input");
    editor.setAttribute("type", "datetime-local");

    editor.style.padding = "3px";
    editor.style.width = "100%";
    editor.style.boxSizing = "border-box";

    const originalValue = cell.getValue(); // có thể là "20241225" hoặc "25/12/2024"

    // Convert value to yyyy-MM-ddTHH:mm format for datetime-local input
    let dateInputValue = '';
    
    if (originalValue) {
        // Nếu là định dạng yyyyMMdd (8 ký tự số)
        if (originalValue.length === 8 && /^\d{8}$/.test(originalValue)) {
            const year = originalValue.substring(0, 4);
            const month = originalValue.substring(4, 6);
            const day = originalValue.substring(6, 8);
            dateInputValue = `${year}-${month}-${day}T00:00`;
        }
        // Nếu là định dạng dd/MM/yyyy
        else if (originalValue.includes('/')) {
            const parts = originalValue.split('/');
            if (parts.length === 3) {
                const day = parts[0].padStart(2, '0');
                const month = parts[1].padStart(2, '0');
                const year = parts[2];
                dateInputValue = `${year}-${month}-${day}T00:00`;
            }
        }
        // Nếu là định dạng yyyy-MM-dd
        else if (originalValue.includes('-') && originalValue.length === 10) {
            dateInputValue = `${originalValue}T00:00`;
        }
    }
    
    editor.value = dateInputValue;

    onRendered(() => {
        editor.focus();
    });

    function successFunc(){
        const selected = editor.value; // ví dụ: "2024-12-25T12:30"
        const originalValue = cell.getValue();
        
        if (selected) {
            // Convert from yyyy-MM-ddTHH:mm back to yyyyMMdd format for storage
            const dt = luxon.DateTime.fromISO(selected);
            let newValue;
            if (dt.isValid) {
                // Format as yyyyMMdd for storage
                newValue = dt.toFormat("yyyyMMdd");
            } else {
                newValue = originalValue; // Keep original if parsing fails
            }
            
            console.log(`[conditionalDateEditor] successFunc: Original='${originalValue}', New='${newValue}', Selected='${selected}'`);
            
            if (newValue !== originalValue) {
                console.log("[conditionalDateEditor] Calling success() with:", newValue);
                success(newValue);
            } else {
                console.log("[conditionalDateEditor] New value is same as original. Calling cancel().");
                cancel();
            }
        } else {
            // User cleared the input
            console.log(`[conditionalDateEditor] successFunc: Input is empty. Original='${originalValue}'`);
            if (cell.getValue()) {
                console.log("[conditionalDateEditor] Original had value, input empty. Calling success('')");
                success("");
            } else {
                console.log("[conditionalDateEditor] Original was also empty. Calling cancel().");
                cancel();
            }
        }
    }

    editor.addEventListener("blur", successFunc);
    editor.addEventListener("keydown", function(e) {
        if (e.key === "Enter") {
            successFunc();
        }
    });

    return editor;
};

// Enhanced formatter that handles both single date and date range formats
function formatDateConditional(cell, formatterParams, onRendered) {
    const value = cell.getValue();
    if (!value) return '';

    // If value contains semicolon, it's a date range
    if (value.includes(';')) {
        // Split by semicolon and convert each date
        const dates = value.split(';');
        const formattedDates = [];
        
        dates.forEach(dateStr => {
            const trimmedDate = dateStr.trim();
            if (trimmedDate) {
                const formatted = convertFromYyyyMMddToDisplay(trimmedDate);
                if (formatted) {
                    formattedDates.push(formatted);
                }
            }
        });
        
        const result = formattedDates.join(';');
        return result;
    }

    // If already in dd/MM/yyyy format, return as is
    if (value.includes('/')) {
        return value;
    }
    
    // If in yyyyMMdd format, convert to dd/MM/yyyy
    if (value.length === 8 && /^\d{8}$/.test(value)) {
        const year = value.substring(0, 4);
        const month = value.substring(4, 6);
        const day = value.substring(6, 8);
        const result = `${day}/${month}/${year}`;
        return result;
    }
    
    // For other formats, use existing formatDate logic as fallback
    return formatDate(cell, formatterParams, onRendered);
}

// Helper function to convert from yyyyMMdd to dd/MM/yyyy (if not already defined)
function convertFromYyyyMMddToDisplay(dateString) {
    if (!dateString) return '';
    
    // Nếu đã là định dạng dd/MM/yyyy thì trả về luôn
    if (dateString.includes('/')) return dateString;
    
    // Nếu là định dạng yyyy-MM-dd thì chuyển đổi
    if (dateString.includes('-')) {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        
        return `${day}/${month}/${year}`;
    }
    
    // Nếu là định dạng yyyyMMdd (8 ký tự)
    if (dateString.length === 8 && /^\d{8}$/.test(dateString)) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        
        return `${day}/${month}/${year}`;
    }
    
    return dateString;
}

// Function to create conditional editable date columns
function createConditionalDateColumn(title, field, customOptions = {}, headerFilterConfig = true, onCellEditedUserCallback = null) {
    return createEditableColumn(
        title, 
        field, 
        conditionalDateEditor, 
        { 
            formatter: formatDateConditional,
            ...customOptions 
        }, 
        headerFilterConfig, 
        onCellEditedUserCallback
    );
}

// Additional helper function for header filter formatting
function formatDateForHeaderFilter(value) {
    if (!value) return '';
    
    // If value contains semicolon, format each date
    if (value.includes(';')) {
        const dates = value.split(';');
        const formattedDates = dates.map(date => convertFromYyyyMMddToDisplay(date.trim())).filter(Boolean);
        return formattedDates.join(';');
    }
    
    // Single date
    return convertFromYyyyMMddToDisplay(value);
}

// Enhanced header filter for date columns that handles both formats
function createDateHeaderFilter(field) {
    return {
        headerFilter: "input",
        headerFilterPlaceholder: "dd/MM/yyyy hoặc dd/MM/yyyy;dd/MM/yyyy",
        headerFilterFunc: function(headerValue, rowValue, rowData, filterParams) {
            if (!headerValue) return true;
            if (!rowValue) return false;
            
            // Format the row value for comparison
            const formattedRowValue = formatDateForHeaderFilter(rowValue);
            
            // Simple contains search
            return formattedRowValue.toLowerCase().includes(headerValue.toLowerCase());
        }
    };
}

// Hàm xử lý logic xóa, có thể được gọi từ nhiều nơi
function handleDeleteAction(buttonElement, cell) {
    const button = $(buttonElement);
    let rowData, id, type, maLK, ngayTao, rowIndex;

    if (cell) {
        // Được gọi từ cellClick - cách đáng tin cậy nhất
        const row = cell.getRow();
        rowData = row.getData();
        id = rowData.id;
        type = button.data('type');
        maLK = rowData.maLK || rowData.ma_lk;
        ngayTao = rowData.ngayTao || rowData.ngay_tao;
        rowIndex = row.getPosition() - 1; // Lấy vị trí hiện tại
    } else {
        // Fallback: Được gọi từ event listener chung, lấy thông tin từ data attributes
        // và DOM traversal. Cách này kém tin cậy hơn.
        const rowElement = button.closest('.tabulator-row');
        if (rowElement.length && rowElement[0]._tabulator_row) {
            const row = rowElement[0]._tabulator_row;
            rowData = row.getData();
            rowIndex = row.getPosition() - 1;
        }
        id = button.data('id') || (rowData ? rowData.id : undefined);
        type = button.data('type');
        maLK = button.data('ma-lk') || (rowData ? (rowData.maLK || rowData.ma_lk) : undefined);
        ngayTao = button.data('ngay-tao') || (rowData ? (rowData.ngayTao || rowData.ngay_tao) : undefined);
    }

    if (!type) {
        type = $('.tab-pane.fade.show.active').attr('id')?.toUpperCase();
    }

    // Validate required data
    if (!id && rowIndex === undefined) {
        Swal.fire('Lỗi', 'Không tìm thấy thông tin bản ghi để xóa.', 'error');
        return;
    }

    // Xác định thông điệp xóa dựa trên loại XML
    let deleteMessage = '';
    if (type === 'XML0') {
        deleteMessage = `Bạn có chắc chắn muốn xóa bản ghi XML0 này?`;
        if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
    } else if (type === 'XML1') {
        deleteMessage = `Bạn có chắc chắn muốn xóa toàn bộ hồ sơ này?`;
        if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
        deleteMessage += `\n\n⚠️ Thao tác này sẽ xóa TẤT CẢ dữ liệu từ XML1-XML15 có cùng Mã LK!`;
    } else {
        deleteMessage = `Bạn có chắc chắn muốn xóa bản ghi ${type} này?`;
        if (maLK) deleteMessage += `\nMã LK: ${maLK}`;
    }

    Swal.fire({
        title: 'Xác nhận xóa',
        text: deleteMessage,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy'
    }).then((result) => {
        if (result.isConfirmed) {
            var isPreviewData = button.hasClass('delete-xml-preview') || window.isDataFromUpload;

            if (isPreviewData) {
                // Dữ liệu từ upload/preview - xóa trực tiếp từ Tabulator bằng rowIndex
                console.log(`Preview mode: Deleting ${type} row at index ${rowIndex}`);
                deleteRowDirectlyFromTabulatorByIndex(type, rowIndex);
            } else {
                // Dữ liệu từ CSDL - gửi request lên server
                console.log(`Database mode: Deleting ${type} with ID ${id}, maLK ${maLK}, ngayTao ${ngayTao}`);
                deleteRowFromDatabase(type, id, maLK, ngayTao, rowIndex);
            }
        }
    });
}

function handleEditAction(buttonElement, cell){
    const button = $(buttonElement);
    let rowData, id, type, maLK, ngayTao, rowIndex;

    if (cell) {
        // Được gọi từ cellClick - cách đáng tin cậy nhất
        const row = cell.getRow();
        rowData = row.getData();
        id = rowData.id;
        type = button.data('type');
        maLK = rowData.maLK || rowData.ma_lk;
        ngayTao = rowData.ngayTao || rowData.ngay_tao;
        rowIndex = row.getPosition() - 1; // Lấy vị trí hiện tại
    } else {
        // Fallback: Được gọi từ event listener chung, lấy thông tin từ data attributes
        // và DOM traversal. Cách này kém tin cậy hơn.
        const rowElement = button.closest('.tabulator-row');
        if (rowElement.length && rowElement[0]._tabulator_row) {
            const row = rowElement[0]._tabulator_row;
            rowData = row.getData();
            rowIndex = row.getPosition() - 1;
        }
        id = button.data('id') || (rowData ? rowData.id : undefined);
        type = button.data('type');
        maLK = button.data('ma-lk') || (rowData ? (rowData.maLK || rowData.ma_lk) : undefined);
        ngayTao = button.data('ngay-tao') || (rowData ? (rowData.ngayTao || rowData.ngay_tao) : undefined);
    }

    if (!type) {
        type = $('.tab-pane.fade.show.active').attr('id')?.toUpperCase();
    }

    // Validate required data
    if (!id && rowIndex === undefined) {
        Swal.fire('Lỗi', 'Không tìm thấy thông tin bản ghi để cập nhật.', 'error');
        return;
    }

    console.log('handle update action: '+ maLK + ';' + ngayTao);

    openEditPreviewModal(maLK, ngayTao);
}