<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary elevation-4">
  <!-- Brand Logo -->
  <a href="{% url 'home' %}" class="brand-link">
    <img src="{{ STATIC_URL }}AdminLTE-3.0.1/dist/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8" />
    <span class="brand-text font-weight-light">LAN Insight</span>
  </a>

  <!-- Sidebar -->
  <div class="sidebar">
    <!-- Sidebar user panel (optional) -->
    <div class="user-panel mt-3 pb-3 mb-3 d-flex">
      <div class="image">
        <img src="{{ STATIC_URL }}AdminLTE-3.0.1/dist/img/user2-160x160.jpg" class="img-circle elevation-2" alt="User Image" />
      </div>
      <div class="info">
        <a href="#" class="d-block">
          {% if user.is_authenticated %}
            {{ user.username }}
          {% else %}
            Khách
          {% endif %}
        </a>
      </div>
    </div>

    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
        <!--  Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library  -->
        {% if 'system.view_dashboard' in user_permissions or user.is_superuser %}
          <li class="nav-item">
            <a href="{% url 'home' %}" class="nav-link {% if request.path == '/' %}active{% endif %}">
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>Dashboard</p>
            </a>
          </li>
        {% endif %}
        {% if 'users.view' in user_permissions or 'users.add' in user_permissions or 'users.edit' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'users' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'users' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-users"></i>
              <p>
                Quản lý người dùng
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'users.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'users:list' %}" class="nav-link {% if request.path == '/users/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Danh sách người dùng</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'devices.view' in user_permissions or 'devices.add' in user_permissions or 'devices.edit' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'devices' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'devices' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-desktop"></i>
              <p>
                Quản lý thiết bị
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'devices.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:device_dashboard' %}" class="nav-link {% if request.path == '/devices/dashboard/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Dashboard</p>
                  </a>
                </li>
              {% endif %}
              {% if 'devices.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:device_list' %}" class="nav-link {% if request.path == '/devices/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Danh sách thiết bị</p>
                  </a>
                </li>
              {% endif %}
              {% if 'devices.add' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:device_scan' %}" class="nav-link {% if request.path == '/devices/scan/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Quét thiết bị mới</p>
                  </a>
                </li>
              {% endif %}
              {% if 'devices.add' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:device_create' %}" class="nav-link {% if request.path == '/devices/create/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Thêm thiết bị mới</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'departments.view' in user_permissions or 'departments.add' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'departments' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'departments' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-list"></i>
              <p>
                Quản lý danh mục
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'departments.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'departments:department_list' %}" class="nav-link {% if request.path == '/departments/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Khoa/Phòng ban</p>
                  </a>
                </li>
              {% endif %}
              {% if 'departments.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'departments:position_list' %}" class="nav-link {% if 'departments/positions' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Chức vụ</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}

        {% if 'danhmucbv.view' in user_permissions or 'danhmucbv.add' in user_permissions or 'danhmucbv.edit' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'danhmucbv' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'danhmucbv' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-hospital"></i>
              <p>
                Danh mục tại Bệnh viện
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'danhmucbv.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmucbv:thietbiyte_list' %}" class="nav-link {% if 'danhmucbv/thietbiyte' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Thiết bị y tế</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmucbv.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmucbv:giuongbenh_list' %}" class="nav-link {% if 'danhmucbv/giuongbenh' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Giường bệnh</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmucbv.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmucbv:thuoc_list' %}" class="nav-link {% if 'danhmucbv/thuoc' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Thuốc</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmucbv.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmucbv:dichvukythuat_list' %}" class="nav-link {% if 'danhmucbv/dichvukythuat' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Dịch vụ kỹ thuật</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmucbv.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmucbv:vattuyyte_list' %}" class="nav-link {% if 'danhmucbv/vattuyyte' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Vật tư y tế</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}

        {% if 'danhmuc130.view' in user_permissions or 'danhmuc130.add' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'danhmuc130' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'danhmuc130' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-clipboard-list"></i>
              <p>
                Danh mục QĐ 130
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:doituongkcb_list' %}" class="nav-link {% if 'danhmuc130/doituongkcb' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Đối tượng KCB</p>
                  </a>
                </li>
              {% endif %}
            </ul>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuc130:quoctich_list' %}" class="nav-link {% if 'danhmuc130/quoctich' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Quốc tịch</p>
                </a>
              </li>
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:nghenghiep_list' %}" class="nav-link {% if 'danhmuc130/nghenghiep' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Nghề nghiệp</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:matainan_list' %}" class="nav-link {% if 'danhmuc130/matainan' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Mã tai nạn</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:ketquadieutri_list' %}" class="nav-link {% if 'danhmuc130/ketquadieutri' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Kết quả điều trị</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:loairavien_list' %}" class="nav-link {% if 'danhmuc130/loairavien' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Loại ra viện</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phamvithanhtoan_list' %}" class="nav-link {% if 'danhmuc130/phamvithanhtoan' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phạm vi thanh toán</p>
                  </a>
                </li>
              {% endif %}
              {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phuongthucthanhtoan_list' %}" class="nav-link {% if 'danhmuc130/phuongthucthanhtoan' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phương thức thanh toán</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_nguonthanhtoan' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:nguonthanhtoan_list' %}" class="nav-link {% if 'danhmuc130/nguonthanhtoan' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Nguồn thanh toán</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_phuongphapvocam' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phuongphapvocam_list' %}" class="nav-link {% if 'danhmuc130/phuongphapvocam' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phương pháp vô cảm</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_phacdohiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phacdohiv_list' %}" class="nav-link {% if 'danhmuc130/phacdohiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phác đồ điều trị HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_bacphacdohiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:bacphacdohiv_list' %}" class="nav-link {% if 'danhmuc130/bacphacdohiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Bậc phác đồ điều trị HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_dieutrilao' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:dieutrilao_list' %}" class="nav-link {% if 'danhmuc130/dieutrilao' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Điều trị lao</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_phacdolao' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phacdolao_list' %}" class="nav-link {% if 'danhmuc130/phacdolao' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phác đồ điều trị lao</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_lydoxetnghiemhiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:lydoxetnghiemhiv_list' %}" class="nav-link {% if 'danhmuc130/lydoxetnghiemhiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Lý do xét nghiệm HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_ketquaxetnghiemhiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:ketquaxetnghiemhiv_list' %}" class="nav-link {% if 'danhmuc130/ketquaxetnghiemhiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Kết quả xét nghiệm HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_loaibenhnhanhiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:loaibenhnhanhiv_list' %}" class="nav-link {% if 'danhmuc130/loaibenhnhanhiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Loại bệnh nhân HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_tinhtrangdangkyhiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:tinhtrangdangkyhiv_list' %}" class="nav-link {% if 'danhmuc130/tinhtrangdangkyhiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Tình trạng đăng ký HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_lanxetnghiemhiv' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:lanxetnghiemhiv_list' %}" class="nav-link {% if 'danhmuc130/lanxetnghiemhiv' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Lần xét nghiệm HIV</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_phuongphapxuly' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:phuongphapxuly_list' %}" class="nav-link {% if 'danhmuc130/phuongphapxuly' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Mã phương pháp xử lý</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_makhamgdyk' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:makhamgdyk_list' %}" class="nav-link {% if 'danhmuc130/makhamgdyk' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Mã khám GDYK</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_chedohuonggdyk' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:chedohuonggdyk_list' %}" class="nav-link {% if 'danhmuc130/chedohuonggdyk' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Chế độ hưởng GDYK</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_dangkhuyettat' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:dangkhuyettat_list' %}" class="nav-link {% if 'danhmuc130/dangkhuyettat' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Dạng khuyết tật</p>
                  </a>
                </li>
              {% endif %}
              {% if 'view_mucdokhuyettat' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'danhmuc130:mucdokhuyettat_list' %}" class="nav-link {% if 'danhmuc130/mucdokhuyettat' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Mức độ khuyết tật</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'danhmuc130.view' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'view_danhmuckhac' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'view_danhmuckhac' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-calendar-alt"></i>
              <p>
                Danh mục khác
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuckhac:tinh_list' %}" class="nav-link {% if 'danhmuckhac/tinh' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh mục Tỉnh/Thành phố</p>
                </a>
              </li>
            </ul>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuckhac:quanhuyen_list' %}" class="nav-link {% if 'danhmuckhac/quanhuyen' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh mục Quận huyện</p>
                </a>
              </li>
            </ul>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuckhac:xaphuong_list' %}" class="nav-link {% if 'danhmuckhac/xaphuong' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh mục Xã phường</p>
                </a>
              </li>
            </ul>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuckhac:noi_dkbd_list' %}" class="nav-link {% if 'danhmuckhac/noi-dkbd' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh mục Nơi đăng kí ban đầu</p>
                </a>
              </li>
            </ul>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'danhmuckhac:pvcm_list' %}" class="nav-link {% if 'danhmuckhac/pvcm' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Danh mục Phạm vi chuyên môn</p>
                </a>
              </li>
            </ul>
          </li>
        {% endif %}
        {% if 'emails.view' in user_permissions or 'emails.send' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'emails' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'emails' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-envelope"></i>
              <p>
                Email nội bộ{% if unread_count > 0 %}
                  <span class="badge badge-danger right">{{ unread_count }}</span>
                {% endif %}
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'emails.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:dashboard' %}" class="nav-link {% if request.path == '/emails/' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <p>Dashboard</p>
                  </a>
                </li>
              {% endif %}
              {% if 'emails.send' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:compose' %}" class="nav-link {% if 'emails/compose' in request.path %}active{% endif %}">
                    <i class="fas fa-pen nav-icon"></i>
                    <p>Soạn tin nhắn</p>
                  </a>
                </li>
              {% endif %}
              {% if 'emails.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:inbox' %}" class="nav-link {% if 'emails/inbox' in request.path %}active{% endif %}">
                    <i class="fas fa-inbox nav-icon"></i>
                    <p>
                      Hộp thư đến{% if unread_count > 0 %}
                        <span class="badge badge-danger right">{{ unread_count }}</span>
                      {% endif %}
                    </p>
                  </a>
                </li>
              {% endif %}
              {% if 'emails.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:drafts' %}" class="nav-link {% if 'emails/drafts' in request.path %}active{% endif %}">
                    <i class="far fa-edit nav-icon"></i>
                    <p>Thư nháp</p>
                  </a>
                </li>
              {% endif %}
              {% if 'emails.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:sent' %}" class="nav-link {% if 'emails/sent' in request.path %}active{% endif %}">
                    <i class="far fa-paper-plane nav-icon"></i>
                    <p>Thư đã gửi</p>
                  </a>
                </li>
              {% endif %}
              {% if 'emails.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'emails:trash' %}" class="nav-link {% if 'emails/trash' in request.path %}active{% endif %}">
                    <i class="far fa-trash-alt nav-icon"></i>
                    <p>Thùng rác</p>
                    <small class="text-muted">(Xóa sau 7 ngày)</small>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'typingpractice.view' in user_permissions or 'typingpractice.take_exam' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'typing' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'typing' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-keyboard"></i>
              <p>
                Luyện tập gõ chữ
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'typingpractice.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'typingpractice:dashboard' %}" class="nav-link {% if request.path == '/typing/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Dashboard</p>
                  </a>
                </li>
              {% endif %}
              {% if 'typingpractice.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'typingpractice:exercise_list' %}" class="nav-link {% if 'typing/exercises' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Bài tập gõ</p>
                  </a>
                </li>
              {% endif %}
              {% if 'typingpractice.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'typingpractice:user_results' %}" class="nav-link {% if 'typing/results' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Kết quả của tôi</p>
                  </a>
                </li>
              {% endif %}

              {% if 'typingpractice.take_exam' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'typingpractice:exam_list' %}" class="nav-link {% if 'typing/exams' in request.path and not 'admin' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Bài thi</p>
                  </a>
                </li>
              {% endif %}
              {% if 'typingpractice.manage' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'typingpractice:admin_exam_results' %}" class="nav-link {% if 'typing/admin/exam-results' in request.path %}active{% endif %}">
                    <i class="fas fa-tasks nav-icon"></i>
                    <p>Quản lý kết quả thi</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'schedules.view' in user_permissions or 'schedules.add' in user_permissions or 'schedules.edit' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'schedules' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'schedules' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-calendar-alt"></i>
              <p>
                Lịch trực
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="{% url 'schedules:my_schedule' %}" class="nav-link {% if 'schedules/my-schedule' in request.path %}active{% endif %}">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Lịch trực</p>
                </a>
              </li>
              {% comment %} {% if 'view_dutyschedule' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'schedules:dashboard' %}" class="nav-link {% if request.path == '/schedules/' %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Dashboard</p>
                  </a>
                </li>
              {% endif %} {% endcomment %}
              {% comment %} {% if 'view_dutyassignment' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'schedules:assignment_list' %}" class="nav-link {% if 'schedules/assignments' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phân công trực</p>
                  </a>
                </li>
              {% endif %} {% endcomment %}
              {% if 'schedules.add' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'schedules:integrated_assignment' %}" class="nav-link {% if 'schedules/integrated-assignment' in request.path %}active{% endif %}">
                    <i class="fas fa-th nav-icon"></i>
                    <p>Phân công trực</p>
                  </a>
                </li>
              {% endif %}
              {% if 'schedules.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'schedules:shift_list' %}" class="nav-link {% if 'schedules/shifts' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Ca trực</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'xml4750.view' in user_permissions or 'xml4750.add' in user_permissions or 'xml4750.edit' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'xml4750' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'xml4750' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-file-code"></i>
              <p>
                Quản lý XML
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'xml4750.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'xml4750:list_xml' %}" class="nav-link {% if 'xml4750/list' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Danh sách XML</p>
                  </a>
                </li>
              {% endif %}
              {% if 'xml4750.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'xml4750:doi_chieu_hs_bh' %}" class="nav-link {% if 'xml4750/doi_chieu_hs_bh' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Đối chiếu hồ sơ BHYT</p>
                  </a>
                </li>
              {% endif %}
              {% if 'xml4750.edit' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'xml4750:validation_config' %}" class="nav-link {% if 'xml4750/validation_config' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Cấu hình kiểm tra XML</p>
                  </a>
                </li>
              {% endif %}
              {% if 'xml4750.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'xml4750:xml_reports' %}" class="nav-link {% if 'xml4750/xml_reports' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Báo cáo tổng hợp</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'reports.view' in user_permissions or 'reports.create' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'reports' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'reports' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-chart-bar"></i>
              <p>
                Báo cáo trực quan
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'reports.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'reports:visual_report_list' %}" class="nav-link {% if request.path == '/reports/list/' %}active{% endif %}">
                    <i class="fas fa-paint-brush nav-icon"></i>
                    <p>Danh sách báo cáo</p>
                  </a>
                </li>
              {% endif %}

              {% if 'reports.create' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'reports:visual_report_create' %}" class="nav-link {% if request.path == '/reports/create/' %}active{% endif %}">
                    <i class="fas fa-plus-circle nav-icon"></i>
                    <p>Tạo báo cáo mới</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="{% url 'reports:visual_report_create_new' %}" class="nav-link {% if request.path == '/reports/create/new/' %}active{% endif %}">
                    <i class="fas fa-plus-square nav-icon"></i>
                    <p>Tạo báo cáo (Giao diện mới)</p>
                  </a>
                </li>
              {% endif %}

              {% if 'reports.view' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'reports:visual_report_template_list' %}" class="nav-link {% if request.path == '/reports/templates/' %}active{% endif %}">
                    <i class="fas fa-palette nav-icon"></i>
                    <p>Mẫu báo cáo</p>
                  </a>
                </li>
              {% endif %}

              <li class="nav-item">
                <a href="{% url 'reports:visual_report_guide' %}" class="nav-link {% if request.path == '/reports/guide/' %}active{% endif %}">
                  <i class="fas fa-question-circle nav-icon"></i>
                  <p>Hướng dẫn sử dụng</p>
                </a>
              </li>
            </ul>
          </li>
        {% endif %}
        {% if 'system.monitor' in user_permissions or 'devices.monitor' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'activities' in request.path or 'alerts' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'activities' in request.path or 'alerts' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-chart-pie"></i>
              <p>
                Giám sát hoạt động
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'devices.monitor' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:activity_list' %}" class="nav-link {% if 'activities' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Hoạt động thiết bị</p>
                  </a>
                </li>
              {% endif %}
              {% if 'system.monitor' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'devices:alert_list' %}" class="nav-link {% if 'alerts' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Cảnh báo</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        {% if 'permissions.view' in user_permissions or 'system.manage' in user_permissions or user.is_superuser %}
          <li class="nav-item has-treeview {% if 'admin' in request.path %}menu-open{% endif %}">
            <a href="#" class="nav-link {% if 'admin' in request.path %}active{% endif %}">
              <i class="nav-icon fas fa-cogs"></i>
              <p>
                Quản trị hệ thống
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              {% if 'system.manage' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'admin-agent-updates' %}" class="nav-link {% if 'agent-updates' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Cập nhật Agent</p>
                  </a>
                </li>
              {% endif %}
              {% if 'permissions.view_roles' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'permissions:role_list' %}" class="nav-link {% if 'permissions/roles' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Quản lý vai trò</p>
                  </a>
                </li>
              {% endif %}
              {% if 'permissions.view_permissions' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="{% url 'permissions:user_role_list' %}" class="nav-link {% if 'permissions/user-roles' in request.path %}active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Phân quyền người dùng</p>
                  </a>
                </li>
              {% endif %}
              {% if 'system.full_access' in user_permissions or user.is_superuser %}
                <li class="nav-item">
                  <a href="#" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Cài đặt hệ thống</p>
                  </a>
                </li>
              {% endif %}
            </ul>
          </li>
        {% endif %}
        <li class="nav-header">TÀI KHOẢN</li>
        {% if user.is_authenticated %}
          <li class="nav-item">
            <a href="{% url 'users:profile' %}" class="nav-link">
              <i class="nav-icon fas fa-user"></i>
              <p>Hồ sơ</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="{% url 'users:logout' %}" class="nav-link">
              <i class="nav-icon fas fa-sign-out-alt"></i>
              <p>Đăng xuất</p>
            </a>
          </li>
        {% else %}
          <li class="nav-item">
            <a href="{% url 'users:login' %}" class="nav-link">
              <i class="nav-icon fas fa-sign-in-alt"></i>
              <p>Đăng nhập</p>
            </a>
          </li>
        {% endif %}
      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
</aside>
