{% extends 'layouts/base.html' %}
{% load static %}
{% load schedules_tags %}

{% block title %}Danh sách lịch trực{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/daterangepicker/daterangepicker.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2/css/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
<style>
  .status-badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
  }
  .status-active {
    background-color: #28a745;
    color: white;
  }
  .status-upcoming {
    background-color: #17a2b8;
    color: white;
  }
  .status-completed {
    background-color: #6c757d;
    color: white;
  }
  .status-draft {
    background-color: #ffc107;
    color: #343a40;
  }
</style>
{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Danh sách lịch trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item active">Danh sách lịch trực</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Danh sách lịch trực</h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          
          <div class="card-body">
            <!-- Bộ lọc -->
            <div class="row mb-3">
              <div class="col-md-12">
                <form method="get" class="form-inline">
                  <div class="form-group mr-2 mb-2">
                    <input type="text" name="search" class="form-control" placeholder="Tìm kiếm..." value="{{ search_query }}">
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <select name="status" class="form-control">
                      <option value="">-- Tất cả trạng thái --</option>
                      <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Đang diễn ra</option>
                      <option value="upcoming" {% if status_filter == 'upcoming' %}selected{% endif %}>Sắp tới</option>
                      <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Đã hoàn thành</option>
                      {% if user.is_superuser or user.is_staff %}
                      <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>Bản nháp</option>
                      {% endif %}
                    </select>
                  </div>
                  
                  <div class="form-group mr-2 mb-2">
                    <select name="department" class="form-control select2">
                      <option value="">-- Tất cả khoa/phòng ban --</option>
                      {% for dept in departments %}
                      <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:"i" %}selected{% endif %}>{{ dept.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                  
                  <button type="submit" class="btn btn-primary mb-2">
                    <i class="fas fa-search"></i> Lọc
                  </button>
                  
                  <a href="{% url 'schedules:schedule_list' %}" class="btn btn-default mb-2 ml-2">
                    <i class="fas fa-sync"></i> Đặt lại
                  </a>
                </form>
              </div>
            </div>
            
            <div class="mb-3">
              <a href="{% url 'schedules:schedule_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm lịch trực mới
              </a>
            </div>
            
            {% if schedules %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px">#</th>
                    <th>Tiêu đề</th>
                    <th>Khoa/Phòng ban</th>
                    <th>Thời gian</th>
                    <th>Trạng thái</th>
                    <th>Phân công</th>
                    <th>Người tạo</th>
                    <th style="width: 120px">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {% for schedule in schedules %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ schedule.title }}</td>
                    <td>{{ schedule.department.name }}</td>
                    <td>{{ schedule.start_date|date:"d/m/Y" }} - {{ schedule.end_date|date:"d/m/Y" }}</td>
                    <td>
                      {% if schedule.status == 'active' %}
                      <span class="status-badge status-active">Đang diễn ra</span>
                      {% elif schedule.status == 'upcoming' %}
                      <span class="status-badge status-upcoming">Sắp tới</span>
                      {% elif schedule.status == 'completed' %}
                      <span class="status-badge status-completed">Đã hoàn thành</span>
                      {% elif schedule.status == 'draft' %}
                      <span class="status-badge status-draft">Bản nháp</span>
                      {% endif %}
                    </td>
                    <td>{{ schedule_stats|get_item:schedule.id|default:"0" }}</td>
                    <td>{{ schedule.created_by.get_full_name }}</td>
                    <td>
                      <div class="btn-group">
                        <a href="{% url 'schedules:schedule_detail' schedule.id %}" class="btn btn-sm btn-info" title="Xem chi tiết">
                          <i class="fas fa-eye"></i>
                        </a>
                        {% if user.is_superuser or user.is_staff or user == schedule.created_by %}
                        <a href="{% url 'schedules:schedule_edit' schedule.id %}" class="btn btn-sm btn-primary" title="Chỉnh sửa">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-danger btn-delete-schedule" data-id="{{ schedule.id }}" title="Xóa">
                          <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            
            {% if schedules.has_other_pages %}
            <div class="pagination mt-3">
              <ul class="pagination">
                {% if schedules.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">&laquo; Đầu</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ schedules.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">Trước</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&laquo; Đầu</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Trước</span>
                </li>
                {% endif %}
                
                {% for i in schedules.paginator.page_range %}
                  {% if schedules.number == i %}
                  <li class="page-item active">
                    <span class="page-link">{{ i }}</span>
                  </li>
                  {% elif i > schedules.number|add:'-3' and i < schedules.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">{{ i }}</a>
                  </li>
                  {% endif %}
                {% endfor %}
                
                {% if schedules.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ schedules.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">Tiếp</a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ schedules.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}">Cuối &raquo;</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                  <span class="page-link">Tiếp</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">Cuối &raquo;</span>
                </li>
                {% endif %}
              </ul>
            </div>
            {% endif %}
            
            {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i> Không có lịch trực nào.
              <a href="{% url 'schedules:schedule_create' %}" class="alert-link">Thêm lịch trực mới</a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'AdminLTE-3.0.1/plugins/select2/js/select2.full.min.js' %}"></script>
<script src="{% static 'AdminLTE-3.0.1/plugins/sweetalert2/sweetalert2.min.js' %}"></script>
<script>
  $(function () {
    // Khởi tạo Select2
    $('.select2').select2({
      theme: 'bootstrap4'
    });
    
    // Xử lý xóa lịch trực bằng SweetAlert2
    $('.btn-delete-schedule').on('click', function() {
      const scheduleId = $(this).data('id');
      
      // Lấy thông tin lịch trực
      $.ajax({
        url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
        type: 'GET',
        dataType: 'json',
        success: function(data) {
          // Hiển thị SweetAlert2 xác nhận
          Swal.fire({
            title: 'Xác nhận xóa lịch trực',
            html: `
              <div class="text-left">
                <p>Bạn có chắc chắn muốn xóa lịch trực <strong>${data.title}</strong> không?</p>
                <p>Thông tin chi tiết:</p>
                <ul>
                  <li>Khoa/Phòng ban: ${data.department}</li>
                  <li>Thời gian: ${data.start_date} - ${data.end_date}</li>
                  <li>Số phân công: ${data.assignment_count}</li>
                  <li>Người tạo: ${data.created_by}</li>
                </ul>
                ${data.assignment_count > 0 ? '<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Lịch trực này đã có phân công. Xóa lịch trực sẽ xóa tất cả các phân công liên quan.</p>' : ''}
                <p class="text-danger">Hành động này không thể hoàn tác!</p>
              </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
          }).then((result) => {
            if (result.isConfirmed) {
              // Gửi request xóa
              $.ajax({
                url: "{% url 'schedules:schedule_delete' 0 %}".replace('0', scheduleId),
                type: 'POST',
                data: {
                  'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                dataType: 'json',
                success: function(response) {
                  // Hiển thị thông báo thành công
                  Swal.fire({
                    title: 'Đã xóa!',
                    text: response.message,
                    icon: 'success'
                  }).then(() => {
                    // Reload trang
                    window.location.reload();
                  });
                },
                error: function(xhr) {
                  // Hiển thị thông báo lỗi
                  let errorMessage = 'Đã xảy ra lỗi khi xóa lịch trực.';
                  if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                  }
                  
                  Swal.fire({
                    title: 'Lỗi!',
                    text: errorMessage,
                    icon: 'error'
                  });
                }
              });
            }
          });
        },
        error: function() {
          // Hiển thị thông báo lỗi
          Swal.fire({
            title: 'Lỗi!',
            text: 'Không thể lấy thông tin lịch trực.',
            icon: 'error'
          });
        }
      });
    });
  });
</script>
{% endblock %}
