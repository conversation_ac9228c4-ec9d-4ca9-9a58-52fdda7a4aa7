{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Xác nhận xóa ca trực{% endblock %}

{% block content %}
<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0 text-dark">Xác nhận xóa ca trực</h1>
      </div>
      <div class="col-sm-6">
        <ol class="breadcrumb float-sm-right">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:dashboard' %}">Lịch trực</a></li>
          <li class="breadcrumb-item"><a href="{% url 'schedules:shift_list' %}">Ca trực</a></li>
          <li class="breadcrumb-item active"><PERSON><PERSON><PERSON> nhận xóa</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-danger">
          <div class="card-header">
            <h3 class="card-title">Xác nhận xóa ca trực</h3>
          </div>
          
          <div class="card-body">
            {% if assignment_count > 0 %}
            <div class="alert alert-danger">
              <h5><i class="icon fas fa-ban"></i> Không thể xóa!</h5>
              <p>Ca trực <strong>{{ shift.name }}</strong> đang được sử dụng trong {{ assignment_count }} phân công lịch trực.</p>
              <p>Vui lòng xóa tất cả các phân công sử dụng ca trực này trước khi xóa ca trực.</p>
            </div>
            {% else %}
            <div class="alert alert-warning">
              <h5><i class="icon fas fa-exclamation-triangle"></i> Cảnh báo!</h5>
              <p>Bạn có chắc chắn muốn xóa ca trực <strong>{{ shift.name }}</strong> không?</p>
              <p>Hành động này không thể hoàn tác.</p>
            </div>
            
            <div class="table-responsive">
              <table class="table table-bordered">
                <tr>
                  <th style="width: 200px">Tên ca trực</th>
                  <td>{{ shift.name }}</td>
                </tr>
                <tr>
                  <th>Thời gian</th>
                  <td>{{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}</td>
                </tr>
                <tr>
                  <th>Thời lượng</th>
                  <td>{{ shift.duration }} giờ</td>
                </tr>
                <tr>
                  <th>Màu hiển thị</th>
                  <td>
                    <span class="badge" style="background-color: {{ shift.color }}; color: #fff; padding: 5px 10px;">
                      {{ shift.color }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <th>Mô tả</th>
                  <td>{{ shift.description|default:"Không có mô tả" }}</td>
                </tr>
              </table>
            </div>
            
            <form method="post" action="{% url 'schedules:shift_delete' shift.id %}">
              {% csrf_token %}
              <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash"></i> Xác nhận xóa
              </button>
              <a href="{% url 'schedules:shift_list' %}" class="btn btn-default">
                <i class="fas fa-times"></i> Hủy
              </a>
            </form>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}
